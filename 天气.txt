P7 天气预报项目 
7.1项目概述 
stylesheet界面美化
Json数据解析
HTTP通信
自定义控件绘制温度
多控件
代码整合调试能力
7.2 stylesheet样式 
设置边框弧度
border-radius: 4px;
设置某方向边框弧度
border-bottom-left-radius: 0px;
 border-bottom-right-radius: 0px;
设置背景颜色
background-color: rgba(60, 60, 60, 100);
父控件影响
父控件指定某类控件的样式，子控件都要遵守此样式进行显示，除非子控件内部有做相关修改
QLabel {
 background-color: rgba(0, 200, 200, 200);
 border-radius: 4px;
 }
 7.3 窗体无状态栏-关闭 
设置无状态栏
setWindowFlag(Qt::FramelessWindowHint);
设置左键弹窗关闭功能
menuQuit = new QMenu(this);
 QAction *closeAct = new QAction(QIcon(":/res/close.png"), tr("退出"), this);
 menuQuit->addAction(closeAct);
 connect(menuQuit,&QMenu::triggered,this,[=]{
 this->close();
 });
 void Widget::mousePressEvent(QMouseEvent *event)
 {
 if(event->button() == Qt::RightButton){
 //qDebug() << "right Mouse clicked!";
 menuQuit->exec(QCursor::pos());
 }
 }
 7.4 窗口跟随移动 
代码实现
void Widget::mousePressEvent(QMouseEvent *event)
 {
 if(event->button() == Qt::RightButton){
//qDebug() << "right Mouse clicked!";
 menuQuit->exec(QCursor::pos());
 }
 //鼠标当前位置 event->globalPos();，
//窗口当前位置 this->pos()窗口新位置event->globalPos() - mOffset
 if(event->button() == Qt::LeftButton){
 // qDebug() << event->globalPos() << this->pos();
 mOffset = event->globalPos()-this->pos();
 }
 }
 //鼠标左键按下后的移动，导致这个事件被调用，设置窗口的新位置
void Widget::mouseMoveEvent(QMouseEvent *event)
 {
 this->move(event->globalPos() - mOffset);
 }
实现的逻辑
7.5 天气预报数据接口 
第一种：
http://t.weather.itboy.net/api/weather/city/101010100
数据返回：
{"message":"success感谢又拍云(upyun.com)提供CDN赞
助","status":200,"date":"20240122","time":"2024-01-22 11:20:56","cityInfo":
 {"city":"北京市","citykey":"101010100","parent":"北
京","updateTime":"07:16"},"data":
 {"shidu":"30%","pm25":4.0,"pm10":14.0,"quality":"优","wendu":"-16","ganmao":"各类
人群可自由活动","forecast":[{"date":"22","high":"高温 -3℃","low":"低温 -11℃","ymd":"2024-01-22","week":"星期
一","sunrise":"07:30","sunset":"17:20","aqi":24,"fx":"西北风","fl":"3
级","type":"晴","notice":"愿你拥有比阳光明媚的心情"},{"date":"23","high":"高温 
1℃","low":"低温 -9℃","ymd":"2024-01-23","week":"星期
二","sunrise":"07:29","sunset":"17:22","aqi":37,"fx":"西北风","fl":"3
级","type":"晴","notice":"愿你拥有比阳光明媚的心情"},{"date":"24","high":"高温 
4℃","low":"低温 -7℃","ymd":"2024-01-24","week":"星期
三","sunrise":"07:29","sunset":"17:23","aqi":74,"fx":"北风","fl":"2
级","type":"晴","notice":"愿你拥有比阳光明媚的心情"},{"date":"25","high":"高温 
5℃","low":"低温 -8℃","ymd":"2024-01-25","week":"星期
四","sunrise":"07:28","sunset":"17:24","aqi":86,"fx":"西北风","fl":"2级","type":"多
云","notice":"阴晴之间，谨防紫外线侵扰"},{"date":"26","high":"高温 5℃","low":"低温 -7℃","ymd":"2024-01-26","week":"星期
五","sunrise":"07:27","sunset":"17:25","aqi":79,"fx":"北风","fl":"2
级","type":"晴","notice":"愿你拥有比阳光明媚的心情"},{"date":"27","high":"高温 
6℃","low":"低温 -4℃","ymd":"2024-01-27","week":"星期
六","sunrise":"07:26","sunset":"17:26","aqi":53,"fx":"西北风","fl":"2
级","type":"晴","notice":"愿你拥有比阳光明媚的心情"},{"date":"28","high":"高温 
4℃","low":"低温 -5℃","ymd":"2024-01-28","week":"星期
日","sunrise":"07:26","sunset":"17:28","aqi":52,"fx":"北风","fl":"1级","type":"多
云","notice":"阴晴之间，谨防紫外线侵扰"},{"date":"29","high":"高温 1℃","low":"低温 -6℃","ymd":"2024-01-29","week":"星期
一","sunrise":"07:25","sunset":"17:29","aqi":22,"fx":"东北风","fl":"1
级","type":"晴","notice":"愿你拥有比阳光明媚的心情"},{"date":"30","high":"高温 
3℃","low":"低温 -6℃","ymd":"2024-01-30","week":"星期
二","sunrise":"07:24","sunset":"17:30","aqi":34,"fx":"东风","fl":"2
级","type":"阴","notice":"不要被阴云遮挡住好心情"},{"date":"31","high":"高温 
4℃","low":"低温 -4℃","ymd":"2024-01-31","week":"星期
三","sunrise":"07:23","sunset":"17:31","aqi":48,"fx":"东南风","fl":"2
级","type":"阴","notice":"不要被阴云遮挡住好心情"},{"date":"01","high":"高温 
8℃","low":"低温 -3℃","ymd":"2024-02-01","week":"星期
四","sunrise":"07:22","sunset":"17:32","aqi":42,"fx":"西风","fl":"1
级","type":"阴","notice":"不要被阴云遮挡住好心情"},{"date":"02","high":"高温 
7℃","low":"低温 -3℃","ymd":"2024-02-02","week":"星期
五","sunrise":"07:21","sunset":"17:34","aqi":59,"fx":"东南风","fl":"1
级","type":"阴","notice":"不要被阴云遮挡住好心情"},{"date":"03","high":"高温 
2℃","low":"低温 -4℃","ymd":"2024-02-03","week":"星期
六","sunrise":"07:20","sunset":"17:35","aqi":41,"fx":"南风","fl":"2级","type":"小
雪","notice":"小雪虽美，赏雪别着凉"},{"date":"04","high":"高温 2℃","low":"低温 -5℃","ymd":"2024-02-04","week":"星期
日","sunrise":"07:19","sunset":"17:36","aqi":45,"fx":"西北风","fl":"1级","type":"多
云","notice":"阴晴之间，谨防紫外线侵扰"},{"date":"05","high":"高温 3℃","low":"低温 -5℃","ymd":"2024-02-05","week":"星期
一","sunrise":"07:18","sunset":"17:37","aqi":48,"fx":"北风","fl":"1级","type":"多
云","notice":"阴晴之间，谨防紫外线侵扰"}],"yesterday":{"date":"21","high":"高温 -5℃","low":"低温 -10℃","ymd":"2024-01-21","week":"星期
日","sunrise":"07:31","sunset":"17:19","aqi":26,"fx":"西北风","fl":"4
级","type":"晴","notice":"愿你拥有比阳光明媚的心情"}}}
第二种：
http://v1.yiketianqi.com/api?
 unescape=1&version=v61&appid=26371314&appsecret=qOhoD413
数据返回
{"cityid":"101230101","date":"2024-01-22","week":"星期
一","update_time":"11:29","city":"福州","cityEn":"fuzhou","country":"中
国","countryEn":"China","wea":"阴","wea_img":"yin","tem":"8.7","tem1":"7","tem2":"-1","win":"东北风","win_speed":"2
级","win_meter":"4km\/h","humidity":"78%","visibility":"10km","pressure":"1019","
 air":"21","air_pm25":"21","air_level":"优","air_tips":"各类人群可多参加户外活动，多呼吸
一下清新的空气。","alarm":{"alarm_type":"降温","alarm_level":"蓝色","alarm_title":"福
建省福州市发布降温蓝色预警","alarm_content":"福州市气象台2024年01月22日09时44分继续发布降温
蓝色预警信号：受寒潮影响，今天到24日我市气温继续下降，日最低气温过程降幅可达9～12℃；今天到24日
夜晨气温较低；过程最低气温晋安区山区可达-3～0℃，有结冰；其余地区1～3℃，有霜或霜冻；22日傍晚到
23日上午部分乡镇将出现小雪或雨夹雪。请注意防范！（预警信息来源：国家预警信息发布中
心）"},"rain_pcpn":"2.8","uvIndex":"2","uvDescription":"低","wea_day":"小
雨","wea_day_img":"yu","wea_night":"小
雨","wea_night_img":"yu","sunrise":"06:50","sunset":"17:36","aqi":
 {"update_time":"10:47","air":"21","air_level":"优","air_tips":"各类人群可多参加户外活
动，多呼吸一下清新的空
气。","pm25":"14","pm25_desc":"优","pm10":"21","pm10_desc":"优","o3":"42","o3_desc
 ":"","no2":"17","no2_desc":"","so2":"5","so2_desc":"","co":"0.8","co_desc":"","ko
 uzhao":"不用佩戴口罩","yundong":"适宜运动","waichu":"适宜外出","kaichuang":"适宜开
窗","jinghuaqi":"不需要打开"}}
 http://v1.yiketianqi.com/api?
 unescape=1&version=v63&appid=26371314&appsecret=qOhoD413 //带小时

{"cityid":"101230101","date":"2024-01-22","week":"星期
一","update_time":"11:29","city":"福州","cityEn":"fuzhou","country":"中
国","countryEn":"China","wea":"阴","wea_img":"yin","tem":"8.7","tem1":"7","tem2":"-1","win":"东北风","win_speed":"2
级","win_meter":"4km\/h","humidity":"78%","visibility":"10km","pressure":"1019","
 air":"21","air_pm25":"21","air_level":"优","air_tips":"各类人群可多参加户外活动，多呼吸
一下清新的空气。","alarm":[{"alarm_type":"降温","alarm_level":"蓝
色","alarm_title":"福建省福州市发布降温蓝色预警","alarm_content":"福州市气象台2024年01月
22日09时44分继续发布降温蓝色预警信号：受寒潮影响，今天到24日我市气温继续下降，日最低气温过程降幅
可达9～12℃；今天到24日夜晨气温较低；过程最低气温晋安区山区可达-3～0℃，有结冰；其余地区1～3℃，
有霜或霜冻；22日傍晚到23日上午部分乡镇将出现小雪或雨夹雪。请注意防范！（预警信息来源：国家预警信
息发布中心）"},{"alarm_type":"降温","alarm_level":"蓝色","alarm_title":"福建省福州市发
布降温蓝色预警","alarm_content":"福州市气象台2024年01月22日09时44分继续发布降温蓝色预警信
号：受寒潮影响，今天到24日我市气温继续下降，日最低气温过程降幅可达9～12℃；今天到24日夜晨气温较
低；过程最低气温晋安区山区可达-3～0℃，有结冰；其余地区1～3℃，有霜或霜冻；22日傍晚到23日上午部
分乡镇将出现小雪或雨夹雪。请注意防范！（预警信息来源：国家预警信息发布中心）"},
 {"alarm_type":"降温","alarm_level":"蓝色","alarm_title":"福建省福州市发布降温蓝色预
警","alarm_content":"福州市气象台2024年01月21日09时29分发布降温蓝色预警信号：受寒潮影响，今
天到23日我市气温持续下降，日最低气温过程降幅可达9～12℃，22～24日夜晨气温较低，过程日最低气温晋
安区北部可达-3～0℃，有霜或霜冻和结冰；其余地区1～3℃，有霜或霜冻，请注意防范！（预警信息来源：国
家预警信息发布中心）"},{"alarm_type":"降温","alarm_level":"蓝色","alarm_title":"福建省
福州市发布降温蓝色预警","alarm_content":"福州市气象台2024年01月21日09时29分发布降温蓝色预警
信号：受寒潮影响，今天到23日我市气温持续下降，日最低气温过程降幅可达9～12℃，22～24日夜晨气温较
低，过程日最低气温晋安区北部可达-3～0℃，有霜或霜冻和结冰；其余地区1～3℃，有霜或霜冻，请注意防
范！（预警信息来源：国家预警信息发布中
心）"}],"rain_pcpn":"2.8","uvIndex":"2","uvDescription":"低","wea_day":"小
雨","wea_day_img":"yu","wea_night":"小
雨","wea_night_img":"yu","sunrise":"06:50","sunset":"17:36","hours":
 [{"hours":"10:00","wea":"小雨","wea_img":"yu","tem":"9","win":"东北
风","win_speed":"2级","vis":"13","aqinum":"21","aqi":"优"},
 {"hours":"11:00","wea":"小雨","wea_img":"yu","tem":"7","win":"东北
风","win_speed":"2级","vis":"12.92","aqinum":"22","aqi":"优"},
 {"hours":"12:00","wea":"小雨","wea_img":"yu","tem":"7","win":"东北
风","win_speed":"2级","vis":"13.02","aqinum":"21","aqi":"优"},
 {"hours":"13:00","wea":"雾","wea_img":"wu","tem":"7","win":"东北风","win_speed":"2
级","vis":"13.02","aqinum":"20","aqi":"优"},
 {"hours":"14:00","wea":"雾","wea_img":"wu","tem":"5","win":"东北风","win_speed":"2
级","vis":"12.92","aqinum":"21","aqi":"优"},{"hours":"15:00","wea":"小
雨","wea_img":"yu","tem":"4","win":"东北风","win_speed":"2
级","vis":"12.78","aqinum":"24","aqi":"优"},{"hours":"16:00","wea":"小
雨","wea_img":"yu","tem":"3","win":"东北风","win_speed":"2
级","vis":"12.21","aqinum":"24","aqi":"优"},{"hours":"17:00","wea":"中
雨","wea_img":"yu","tem":"2","win":"东北风","win_speed":"2
级","vis":"11.77","aqinum":"27","aqi":"优"},{"hours":"18:00","wea":"中
雨","wea_img":"yu","tem":"1","win":"东北风","win_speed":"2
级","vis":"11.42","aqinum":"26","aqi":"优"},{"hours":"19:00","wea":"小
雨","wea_img":"yu","tem":"1","win":"东北风","win_speed":"2
级","vis":"10.77","aqinum":"24","aqi":"优"},{"hours":"20:00","wea":"小
雨","wea_img":"yu","tem":"1","win":"东北风","win_speed":"2
级","vis":"10.13","aqinum":"23","aqi":"优"},{"hours":"21:00","wea":"小
雨","wea_img":"yu","tem":"1","win":"东北风","win_speed":"2
级","vis":"9.3","aqinum":"23","aqi":"优"},{"hours":"22:00","wea":"小
雨","wea_img":"yu","tem":"1","win":"东北风","win_speed":"2
级","vis":"9.11","aqinum":"24","aqi":"优"},{"hours":"23:00","wea":"小
雨","wea_img":"yu","tem":"1","win":"东北风","win_speed":"1
级","vis":"9.11","aqinum":"27","aqi":"优"},
{"hours":"00:00","wea":"雾","wea_img":"wu","tem":"2","win":"东北风","win_speed":"1
级","vis":"8.91","aqinum":"27","aqi":"优"},{"hours":"01:00","wea":"小
雨","wea_img":"yu","tem":"2","win":"东北风","win_speed":"1
级","vis":"8.91","aqinum":"28","aqi":"优"},{"hours":"02:00","wea":"小
雨","wea_img":"yu","tem":"2","win":"东北风","win_speed":"1
级","vis":"8.91","aqinum":"30","aqi":"优"},{"hours":"03:00","wea":"小
雨","wea_img":"yu","tem":"2","win":"东北风","win_speed":"1
级","vis":"8.91","aqinum":"28","aqi":"优"},{"hours":"04:00","wea":"小
雨","wea_img":"yu","tem":"2","win":"北东北风","win_speed":"1
级","vis":"9.28","aqinum":"28","aqi":"优"},
 {"hours":"05:00","wea":"阴","wea_img":"yin","tem":"2","win":"东北
风","win_speed":"1级","vis":"9.83","aqinum":"28","aqi":"优"},
 {"hours":"06:00","wea":"多云","wea_img":"yun","tem":"3","win":"东北
风","win_speed":"1级","vis":"10.7","aqinum":"28","aqi":"优"},
 {"hours":"07:00","wea":"晴","wea_img":"qing","tem":"4","win":"东北
风","win_speed":"1级","vis":"11","aqinum":"28","aqi":"优"},
 {"hours":"08:00","wea":"晴","wea_img":"qing","tem":"5","win":"东北
风","win_speed":"1级","vis":"11.59","aqinum":"28","aqi":"优"},
 {"hours":"09:00","wea":"晴","wea_img":"qing","tem":"6","win":"东北
风","win_speed":"1级","vis":"12.41","aqinum":"27","aqi":"优"},
 {"hours":"10:00","wea":"晴","wea_img":"qing","tem":"7","win":"东北
风","win_speed":"1级","vis":"13.43","aqinum":"27","aqi":"优"},
 {"hours":"11:00","wea":"晴","wea_img":"qing","tem":"8","win":"东北
风","win_speed":"1级","vis":"14.42","aqinum":"27","aqi":"优"},
 {"hours":"12:00","wea":"晴","wea_img":"qing","tem":"8","win":"东北
风","win_speed":"1级","vis":"15.58","aqinum":"25","aqi":"优"},
 {"hours":"13:00","wea":"晴","wea_img":"qing","tem":"9","win":"东北
风","win_speed":"1级","vis":"16.85","aqinum":"24","aqi":"优"}],"aqi":
 {"update_time":"10:47","air":"21","air_level":"优","air_tips":"各类人群可多参加户外活
动，多呼吸一下清新的空
气。","pm25":"14","pm25_desc":"优","pm10":"21","pm10_desc":"优","o3":"42","o3_desc
 ":"","no2":"17","no2_desc":"","so2":"5","so2_desc":"","co":"0.8","co_desc":"","ko
 uzhao":"不用佩戴口罩","yundong":"适宜运动","waichu":"适宜外出","kaichuang":"适宜开
窗","jinghuaqi":"不需要打开"}}
未来7天
http://v1.yiketianqi.com/api?
 unescape=1&version=v9&appid=26371314&appsecret=qOhoD413
数据返回

{"cityid":"101230101","city":"福州","cityEn":"fuzhou","country":"中
国","countryEn":"China","update_time":"2024-01-22 11:47:00","data":[{"day":"22日
（星期一）","date":"2024-01-22","week":"星期
一","wea":"阴","wea_img":"yin","wea_day":"小雨","wea_day_img":"yu","wea_night":"小
雨","wea_night_img":"yu","tem":"8.7","tem1":"7","tem2":"-1","humidity":"79%","vis
 ibility":"11km","pressure":"1019","win":["无持续风向","无持续风向"],"win_speed":"<3
级","win_meter":"2km\/h","sunrise":"06:50","sunset":"17:36","air":"21","air_level
 ":"优","air_tips":"各类人群可多参加户外活动，多呼吸一下清新的空气。","alarm":
 {"alarm_type":"降温","alarm_level":"蓝色","alarm_title":"福建省福州市发布降温蓝色预
警","alarm_content":"福州市气象台2024年01月22日09时44分继续发布降温蓝色预警信号：受寒潮影
响，今天到24日我市气温继续下降，日最低气温过程降幅可达9～12℃；今天到24日夜晨气温较低；过程最低
气温晋安区山区可达-3～0℃，有结冰；其余地区1～3℃，有霜或霜冻；22日傍晚到23日上午部分乡镇将出现
小雪或雨夹雪。请注意防范！（预警信息来源：国家预警信息发布中心）"},"hours":[{"hours":"08
时","wea":"阴","wea_img":"yin","tem":"7","win":"无持续风向","win_speed":"<3级"},
 {"hours":"09时","wea":"小雨","wea_img":"yu","tem":"6","win":"东北风","win_speed":"
 <3级"},{"hours":"10时","wea":"小雨","wea_img":"yu","tem":"6","win":"东北
风","win_speed":"<3级"},{"hours":"11时","wea":"小
雨","wea_img":"yu","tem":"6","win":"东北风","win_speed":"<3级"},{"hours":"12
时","wea":"小雨","wea_img":"yu","tem":"6","win":"东北风","win_speed":"<3级"},
 {"hours":"13时","wea":"小雨","wea_img":"yu","tem":"6","win":"东北风","win_speed":"
 <3级"},{"hours":"14时","wea":"小雨","wea_img":"yu","tem":"6","win":"东北
风","win_speed":"<3级"},{"hours":"15时","wea":"小
雨","wea_img":"yu","tem":"5","win":"东北风","win_speed":"<3级"},{"hours":"16
时","wea":"小雨","wea_img":"yu","tem":"5","win":"东北风","win_speed":"<3级"},
 {"hours":"17时","wea":"小雨","wea_img":"yu","tem":"4","win":"东北风","win_speed":"
 <3级"},{"hours":"18时","wea":"小雨","wea_img":"yu","tem":"4","win":"东北
风","win_speed":"<3级"},{"hours":"19时","wea":"小
雨","wea_img":"yu","tem":"4","win":"东北风","win_speed":"<3级"},{"hours":"20
时","wea":"小雨","wea_img":"yu","tem":"4","win":"东北风","win_speed":"<3级"},
 {"hours":"21时","wea":"小雨","wea_img":"yu","tem":"3","win":"东北风","win_speed":"
 <3级"},{"hours":"22时","wea":"小雨","wea_img":"yu","tem":"3","win":"东北
风","win_speed":"<3级"},{"hours":"23时","wea":"小
雨","wea_img":"yu","tem":"3","win":"东北风","win_speed":"<3级"},{"hours":"00
时","wea":"雨夹雪","wea_img":"yu","tem":"1","win":"东北风","win_speed":"<3级"},
 {"hours":"01时","wea":"雨夹雪","wea_img":"yu","tem":"0","win":"东北
风","win_speed":"<3级"},{"hours":"02时","wea":"雨夹
雪","wea_img":"yu","tem":"0","win":"东北风","win_speed":"<3级"},{"hours":"03
时","wea":"雨夹雪","wea_img":"yu","tem":"0","win":"东北风","win_speed":"<3级"},
 {"hours":"04时","wea":"雨夹雪","wea_img":"yu","tem":"0","win":"东北
风","win_speed":"<3级"},{"hours":"05时","wea":"雨夹
雪","wea_img":"yu","tem":"1","win":"东北风","win_speed":"<3级"},{"hours":"06
时","wea":"小雨","wea_img":"yu","tem":"2","win":"东北风","win_speed":"<3级"},
 {"hours":"07时","wea":"小雨","wea_img":"yu","tem":"2","win":"东北风","win_speed":"
 <3级"}],"index":[{"title":"紫外线指数","level":"最弱","desc":"辐射弱，涂擦SPF8-12防晒护
肤品。"},{"title":"减肥指数","level":"较不宜","desc":"有降水，推荐您在室内进行休闲运
动。"},{"title":"血糖指数","level":"极不易发","desc":"无需担心过敏，可放心外出，享受生
活。"},{"title":"穿衣指数","level":"冷","desc":"建议着棉衣加羊毛衫等冬季服装。"},
 {"title":"洗车指数","level":"不宜","desc":"有雨，雨水和泥水会弄脏爱车。"},{"title":"空气
污染扩散指数","level":"良","desc":"气象条件有利于空气污染物扩
散。"}],"uvIndex":"2","uvDescription":"低"},{"day":"23日（星期二）","date":"2024-01
23","week":"星期二","wea":"多云转晴","wea_img":"yun","wea_day":"多
云","wea_day_img":"yun","wea_night":"晴","wea_night_img":"qing","tem":"10","tem1":
 "10","tem2":"2","humidity":"63","visibility":"","pressure":"","win":["无持续风
向","无持续风向"],"win_speed":"<3
级","win_meter":"","sunrise":"06:50","sunset":"17:37","air":"31","air_level":"优",
 "air_tips":"","alarm":
{"alarm_type":"","alarm_level":"","alarm_content":""},"hours":[{"hours":"08
时","wea":"小雨","wea_img":"yu","tem":"3","win":"东北风","win_speed":"<3级"},
 {"hours":"09时","wea":"多云","wea_img":"yun","tem":"4","win":"东北风","win_speed":"
 <3级"},{"hours":"10时","wea":"多云","wea_img":"yun","tem":"5","win":"东北
风","win_speed":"<3级"},{"hours":"11时","wea":"多
云","wea_img":"yun","tem":"7","win":"北风","win_speed":"<3级"},{"hours":"12
时","wea":"多云","wea_img":"yun","tem":"8","win":"东北风","win_speed":"<3级"},
 {"hours":"13时","wea":"多云","wea_img":"yun","tem":"8","win":"东北风","win_speed":"
 <3级"},{"hours":"14时","wea":"晴","wea_img":"qing","tem":"9","win":"东
风","win_speed":"<3级"},{"hours":"15
时","wea":"晴","wea_img":"qing","tem":"9","win":"东风","win_speed":"<3级"},
 {"hours":"16时","wea":"晴","wea_img":"qing","tem":"8","win":"东风","win_speed":"<3
级"},{"hours":"17时","wea":"晴","wea_img":"qing","tem":"7","win":"东
风","win_speed":"<3级"},{"hours":"18
时","wea":"晴","wea_img":"qing","tem":"6","win":"东风","win_speed":"<3级"},
 {"hours":"19时","wea":"晴","wea_img":"qing","tem":"6","win":"东北风","win_speed":"
 <3级"},{"hours":"20时","wea":"晴","wea_img":"qing","tem":"5","win":"东北
风","win_speed":"<3级"},{"hours":"21
时","wea":"晴","wea_img":"qing","tem":"4","win":"东北风","win_speed":"<3级"},
 {"hours":"22时","wea":"晴","wea_img":"qing","tem":"4","win":"东北风","win_speed":"
 <3级"},{"hours":"23时","wea":"晴","wea_img":"qing","tem":"4","win":"东北
风","win_speed":"<3级"},{"hours":"00
时","wea":"晴","wea_img":"qing","tem":"4","win":"东北风","win_speed":"<3级"},
 {"hours":"01时","wea":"晴","wea_img":"qing","tem":"3","win":"东北风","win_speed":"
 <3级"},{"hours":"02时","wea":"晴","wea_img":"qing","tem":"3","win":"东北
风","win_speed":"<3级"},{"hours":"03
时","wea":"晴","wea_img":"qing","tem":"2","win":"东南风","win_speed":"<3级"},
 {"hours":"04时","wea":"晴","wea_img":"qing","tem":"2","win":"西南风","win_speed":"
 <3级"},{"hours":"05时","wea":"晴","wea_img":"qing","tem":"2","win":"北
风","win_speed":"<3级"},{"hours":"06
时","wea":"晴","wea_img":"qing","tem":"2","win":"北风","win_speed":"<3级"},
 {"hours":"07时","wea":"晴","wea_img":"qing","tem":"3","win":"北风","win_speed":"<3
级"}],"index":[{"title":"紫外线指数","level":"中等","desc":"涂擦SPF大于15、PA+防晒护肤
品。"},{"title":"减肥指数","level":"较适宜","desc":"气温较低，在户外运动请注意增减衣
物。"},{"title":"血糖指数","level":"极不易发","desc":"无需担心过敏，可放心外出，享受生
活。"},{"title":"穿衣指数","level":"较冷","desc":"建议着厚外套加毛衣等服装。"},
 {"title":"洗车指数","level":"较不宜","desc":"路面有积水，车子易被溅上泥水。"},
 {"title":"空气污染扩散指数","level":"中","desc":"易感人群应适当减少室外活
动。"}],"uvIndex":"5","uvDescription":"中等"},{"day":"24日（星期三）","date":"2024
01-24","week":"星期
三","wea":"晴","wea_img":"qing","wea_day":"晴","wea_day_img":"qing","wea_night":"
晴","wea_night_img":"qing","tem":"11","tem1":"11","tem2":"4","humidity":"46","vis
 ibility":"","pressure":"","win":["无持续风向","无持续风向"],"win_speed":"<3
级","win_meter":"","sunrise":"06:50","sunset":"17:38","air":"34","air_level":"优",
 "air_tips":"","alarm":
 {"alarm_type":"","alarm_level":"","alarm_content":""},"hours":[{"hours":"08
时","wea":"晴","wea_img":"qing","tem":"3","win":"西北风","win_speed":"<3级"},
 {"hours":"09时","wea":"晴","wea_img":"qing","tem":"5","win":"西风","win_speed":"<3
级"},{"hours":"10时","wea":"晴","wea_img":"qing","tem":"7","win":"南
风","win_speed":"<3级"},{"hours":"11
时","wea":"晴","wea_img":"qing","tem":"9","win":"东风","win_speed":"<3级"},
 {"hours":"12时","wea":"晴","wea_img":"qing","tem":"9","win":"东风","win_speed":"<3
级"},{"hours":"13时","wea":"晴","wea_img":"qing","tem":"10","win":"东
风","win_speed":"<3级"},{"hours":"14
时","wea":"晴","wea_img":"qing","tem":"10","win":"东风","win_speed":"<3级"},
 {"hours":"15时","wea":"晴","wea_img":"qing","tem":"10","win":"东风","win_speed":"
<3级"},{"hours":"16时","wea":"晴","wea_img":"qing","tem":"9","win":"东
风","win_speed":"<3级"},{"hours":"17
时","wea":"晴","wea_img":"qing","tem":"9","win":"东风","win_speed":"<3级"},
 {"hours":"18时","wea":"晴","wea_img":"qing","tem":"8","win":"东风","win_speed":"<3
级"},{"hours":"19时","wea":"晴","wea_img":"qing","tem":"8","win":"东
风","win_speed":"<3级"},{"hours":"20
时","wea":"晴","wea_img":"qing","tem":"8","win":"东北风","win_speed":"<3级"},
 {"hours":"21时","wea":"晴","wea_img":"qing","tem":"7","win":"东北风","win_speed":"
 <3级"},{"hours":"22时","wea":"晴","wea_img":"qing","tem":"5","win":"东北
风","win_speed":"<3级"},{"hours":"23
时","wea":"晴","wea_img":"qing","tem":"4","win":"北风","win_speed":"<3级"},
 {"hours":"00时","wea":"晴","wea_img":"qing","tem":"4","win":"东南风","win_speed":"
 <3级"},{"hours":"01时","wea":"晴","wea_img":"qing","tem":"4","win":"西南
风","win_speed":"<3级"},{"hours":"02
时","wea":"晴","wea_img":"qing","tem":"4","win":"北风","win_speed":"<3级"},
 {"hours":"03时","wea":"晴","wea_img":"qing","tem":"5","win":"北风","win_speed":"<3
级"},{"hours":"04时","wea":"晴","wea_img":"qing","tem":"5","win":"西北
风","win_speed":"<3级"},{"hours":"05
时","wea":"晴","wea_img":"qing","tem":"5","win":"西北风","win_speed":"<3级"},
 {"hours":"06时","wea":"晴","wea_img":"qing","tem":"5","win":"西北风","win_speed":"
 <3级"},{"hours":"07时","wea":"晴","wea_img":"qing","tem":"5","win":"西北
风","win_speed":"<3级"}],"index":[{"title":"紫外线指数","level":"强","desc":"涂擦SPF
大于15、PA+防晒护肤品。"},{"title":"减肥指数","level":"较适宜","desc":"气温较低，在户外运
动请注意增减衣物。"},{"title":"血糖指数","level":"极不易发","desc":"无需担心过敏，可放心外
出，享受生活。"},{"title":"穿衣指数","level":"较冷","desc":"建议着厚外套加毛衣等服装。"},
 {"title":"洗车指数","level":"适宜","desc":"天气较好，适合擦洗汽车。"},{"title":"空气污染
扩散指数","level":"中","desc":"易感人群应适当减少室外活
动。"}],"uvIndex":"6","uvDescription":"强"},{"day":"25日（星期四）","date":"2024-01
25","week":"星期四","wea":"晴转多
云","wea_img":"yun","wea_day":"晴","wea_day_img":"qing","wea_night":"多
云","wea_night_img":"yun","tem":"13","tem1":"13","tem2":"5","humidity":"51","visi
 bility":"","pressure":"","win":["无持续风向","无持续风向"],"win_speed":"<3
级","win_meter":"","sunrise":"06:50","sunset":"17:39","air":"31","air_level":"优",
 "air_tips":"","alarm":
 {"alarm_type":"","alarm_level":"","alarm_content":""},"hours":[{"hours":"08
时","wea":"晴","wea_img":"qing","tem":"6","win":"西北风","win_speed":"<3级"},
 {"hours":"11时","wea":"晴","wea_img":"qing","tem":"11","win":"东南风","win_speed":"
 <3级"},{"hours":"14时","wea":"晴","wea_img":"qing","tem":"12","win":"东
风","win_speed":"<3级"},{"hours":"17
时","wea":"晴","wea_img":"qing","tem":"13","win":"东北风","win_speed":"<3级"},
 {"hours":"20时","wea":"晴","wea_img":"qing","tem":"8","win":"东北风","win_speed":"
 <3级"},{"hours":"23时","wea":"晴","wea_img":"qing","tem":"7","win":"西
风","win_speed":"<3级"},{"hours":"02时","wea":"多
云","wea_img":"yun","tem":"5","win":"北风","win_speed":"<3级"},{"hours":"05
时","wea":"多云","wea_img":"yun","tem":"5","win":"北风","win_speed":"<3
级"}],"index":[{"title":"紫外线指数","level":"强","desc":"涂擦SPF大于15、PA+防晒护肤
品。"},{"title":"减肥指数","level":"较适宜","desc":"气温较低，在户外运动请注意增减衣
物。"},{"title":"血糖指数","level":"极不易发","desc":"无需担心过敏，可放心外出，享受生
活。"},{"title":"穿衣指数","level":"较冷","desc":"建议着厚外套加毛衣等服装。"},
 {"title":"洗车指数","level":"适宜","desc":"天气较好，适合擦洗汽车。"},{"title":"空气污染
扩散指数","level":"中","desc":"易感人群应适当减少室外活
动。"}],"uvIndex":"6","uvDescription":"强"},{"day":"26日（星期五）","date":"2024-01
26","week":"星期
五","wea":"晴","wea_img":"qing","wea_day":"晴","wea_day_img":"qing","wea_night":"
晴","wea_night_img":"qing","tem":"15","tem1":"15","tem2":"6","humidity":"53","vis
 ibility":"","pressure":"","win":["无持续风向","无持续风向"],"win_speed":"<3
级","win_meter":"","sunrise":"06:49","sunset":"17:39","air":"30","air_level":"优",
 "air_tips":"","alarm":
 {"alarm_type":"","alarm_level":"","alarm_content":""},"hours":[{"hours":"08
时","wea":"多云","wea_img":"yun","tem":"6","win":"西北风","win_speed":"<3级"},
 {"hours":"11时","wea":"多云","wea_img":"yun","tem":"10","win":"东南
风","win_speed":"<3级"},{"hours":"14
时","wea":"晴","wea_img":"qing","tem":"14","win":"东风","win_speed":"<3级"},
 {"hours":"17时","wea":"晴","wea_img":"qing","tem":"12","win":"东北风","win_speed":"
 <3级"},{"hours":"20时","wea":"晴","wea_img":"qing","tem":"10","win":"东
风","win_speed":"<3级"},{"hours":"23
时","wea":"晴","wea_img":"qing","tem":"8","win":"东风","win_speed":"<3级"},
 {"hours":"02时","wea":"晴","wea_img":"qing","tem":"6","win":"西北风","win_speed":"
 <3级"},{"hours":"05时","wea":"晴","wea_img":"qing","tem":"7","win":"南
风","win_speed":"<3级"}],"index":[{"title":"紫外线指数","level":"强","desc":"涂擦SPF
大于15、PA+防晒护肤品。"},{"title":"减肥指数","level":"较适宜","desc":"天气凉，在户外运动
请注意增减衣物。"},{"title":"血糖指数","level":"极不易发","desc":"无需担心过敏，可放心外
出，享受生活。"},{"title":"穿衣指数","level":"较冷","desc":"建议着厚外套加毛衣等服装。"},
 {"title":"洗车指数","level":"适宜","desc":"天气较好，适合擦洗汽车。"},{"title":"空气污染
扩散指数","level":"中","desc":"易感人群应适当减少室外活
动。"}],"uvIndex":"6","uvDescription":"强"},{"day":"27日（星期六）","date":"2024-01
27","week":"星期六","wea":"多云","wea_img":"yun","wea_day":"多
云","wea_day_img":"yun","wea_night":"多
云","wea_night_img":"yun","tem":"16","tem1":"16","tem2":"8","humidity":"53","visi
 bility":"","pressure":"","win":["无持续风向","无持续风向"],"win_speed":"<3
级","win_meter":"","sunrise":"06:49","sunset":"17:40","air":"36","air_level":"优",
 "air_tips":"","alarm":
 {"alarm_type":"","alarm_level":"","alarm_content":""},"hours":[{"hours":"08
时","wea":"晴","wea_img":"qing","tem":"8","win":"西北风","win_speed":"<3级"},
 {"hours":"11时","wea":"多云","wea_img":"yun","tem":"12","win":"东南
风","win_speed":"<3级"},{"hours":"14时","wea":"多
云","wea_img":"yun","tem":"15","win":"东北风","win_speed":"<3级"},{"hours":"17
时","wea":"多云","wea_img":"yun","tem":"14","win":"东风","win_speed":"<3级"},
 {"hours":"20时","wea":"多云","wea_img":"yun","tem":"13","win":"东风","win_speed":"
 <3级"},{"hours":"23时","wea":"多云","wea_img":"yun","tem":"12","win":"东南
风","win_speed":"<3级"},{"hours":"02时","wea":"多
云","wea_img":"yun","tem":"8","win":"西北风","win_speed":"<3级"},{"hours":"05
时","wea":"多云","wea_img":"yun","tem":"8","win":"南风","win_speed":"<3
级"}],"index":[{"title":"紫外线指数","level":"弱","desc":"辐射较弱，涂擦SPF12-15、
PA+护肤品。"},{"title":"减肥指数","level":"较适宜","desc":"天气凉，在户外运动请注意增减衣
物。"},{"title":"血糖指数","level":"极不易发","desc":"无需担心过敏，可放心外出，享受生
活。"},{"title":"穿衣指数","level":"较冷","desc":"建议着厚外套加毛衣等服装。"},
 {"title":"洗车指数","level":"适宜","desc":"天气较好，适合擦洗汽车。"},{"title":"空气污染
扩散指数","level":"中","desc":"易感人群应适当减少室外活
动。"}],"uvIndex":"5","uvDescription":"中等"},{"day":"28日（星期日）","date":"2024
01-28","week":"星期日","wea":"多云","wea_img":"yun","wea_day":"多
云","wea_day_img":"yun","wea_night":"多
云","wea_night_img":"yun","tem":"17","tem1":"17","tem2":"10","humidity":"62","vis
 ibility":"","pressure":"","win":["无持续风向","无持续风向"],"win_speed":"<3
级","win_meter":"","sunrise":"06:49","sunset":"17:41","air":"33","air_level":"优",
 "air_tips":"","alarm":
 {"alarm_type":"","alarm_level":"","alarm_content":""},"hours":[{"hours":"08
时","wea":"多云","wea_img":"yun","tem":"10","win":"西北风","win_speed":"<3级"},
 {"hours":"11时","wea":"多云","wea_img":"yun","tem":"13","win":"东南
风","win_speed":"<3级"},{"hours":"14时","wea":"多
云","wea_img":"yun","tem":"16","win":"东风","win_speed":"<3级"},{"hours":"17
时","wea":"多云","wea_img":"yun","tem":"15","win":"东风","win_speed":"<3级"},
{"hours":"20时","wea":"多云","wea_img":"yun","tem":"14","win":"东风","win_speed":"
 <3级"},{"hours":"23时","wea":"多云","wea_img":"yun","tem":"12","win":"西
风","win_speed":"<3级"},{"hours":"02时","wea":"多
云","wea_img":"yun","tem":"10","win":"北风","win_speed":"<3级"},{"hours":"05
时","wea":"多云","wea_img":"yun","tem":"10","win":"北风","win_speed":"<3
级"}],"index":[{"title":"紫外线指数","level":"弱","desc":"辐射较弱，涂擦SPF12-15、
PA+护肤品。"},{"title":"减肥指数","level":"较适宜","desc":"天气凉，在户外运动请注意增减衣
物。"},{"title":"血糖指数","level":"极不易发","desc":"无需担心过敏，可放心外出，享受生
活。"},{"title":"穿衣指数","level":"较冷","desc":"建议着厚外套加毛衣等服装。"},
 {"title":"洗车指数","level":"适宜","desc":"天气较好，适合擦洗汽车。"},{"title":"空气污染
扩散指数","level":"中","desc":"易感人群应适当减少室外活
动。"}],"uvIndex":"5","uvDescription":"中等"}],"aqi":
 {"update_time":"10:47","cityid":"101230101","city":"","cityEn":"","country":"","c
 ountryEn":"","air":"21","air_level":"优","air_tips":"各类人群可多参加户外活动，多呼吸一
下清新的空
气。","pm25":"14","pm25_desc":"优","pm10":"21","pm10_desc":"优","o3":"42","o3_desc
 ":"","no2":"17","no2_desc":"","so2":"5","so2_desc":"","co":"0.8","co_desc":"","ko
 uzhao":"不用佩戴口罩","yundong":"适宜运动","waichu":"适宜外出","kaichuang":"适宜开
窗","jinghuaqi":"不需要打开"}}
 7.6 软件开发网络通信架构 
7.6.1 BS架构/CS架构 
在计算机网络和软件开发中，CS架构（Client-Server Architecture，客户端-服务器架构）和BS架构
（Browser-Server Architecture，浏览器-服务器架构）是两种主要的应用程序架构。
CS架构（客户端-服务器架构）
CS架构是一种典型的两层结构，包括客户端和服务器两个部分。在这种架构中，客户端和服务器通过网
络进行通信，每部分都有明确的职责。
1. 客户端：
用户界面通常在客户端呈现。
可以是桌面应用程序、移动应用或专用软件。
负责向服务器发送请求，接收和处理服务器响应。
2. 服务器：
管理数据和业务逻辑。
处理来自客户端的请求，并发送回响应。
通常承载在远程系统上，如数据库服务器、应用服务器等。
3. 特点：
需要为每种操作系统或平台单独开发客户端。
高效的数据处理和响应能力。
在客户端设备上占用资源（如内存和处理能力）。
BS架构（浏览器-服务器架构）
BS架构是一种基于Web的三层或多层架构，主要通过Web浏览器作为客户端访问服务器上的应用程序。
1. 浏览器（客户端）：
使用标准Web浏览器（如Chrome、Firefox等）作为客户端。
无需安装额外的软件，使用HTML、CSS和JavaScript显示内容。
2. 服务器：
和CS架构中的服务器类似，处理业务逻辑和数据存储。
通过Web服务（如HTTP服务器）提供页面和数据。
3. 特点：
跨平台兼容性强，可以在任何支持Web浏览器的设备上运行。
客户端无需安装专用软件，容易维护和更新。
可能依赖网络性能，因为所有操作都在服务器上进行。
对比
部署和维护：BS架构易于部署和维护，而CS架构通常需要在每个客户端单独安装和更新。
性能：CS架构可以更有效地利用客户端的计算资源，适合高性能要求的应用。BS架构依赖于服务器
的性能和网络延迟。
安全性：CS架构中，数据经常在客户端和服务器之间传输，可能需要更复杂的安全措施。BS架构
中，敏感数据主要存储在服务器端。
用户体验：CS架构通常能提供更丰富的用户界面和交互功能。BS架构的用户体验受限于Web技术的
能力。
在实际应用中，选择哪种架构取决于具体的业务需求、目标用户群、性能要求以及开发和维护的成本。
7.6.2 HTTP基本概念 
HTTP（超文本传输协议）是一种用于分布式、协作式和超媒体信息系统的应用层协议。它是万维网
（WWW）的数据通信的基础。了解HTTP的基本概念对于理解现代网络通信至关重要。以下是HTTP的一
些核心概念：
1. 请求和响应
HTTP是一个基于请求-响应模式的协议。客户端（通常是Web浏览器）向服务器发送一个HTTP请求，然
后服务器返回一个HTTP响应。请求包含请求的资源（如网页），而响应包含请求的资源的内容。
2. HTTP方法
HTTP定义了一系列的方法来表明对资源的不同操作，最常用的包括：
GET: 用于请求资源。
POST: 用于提交数据给服务器（例如，表单数据）。
PUT: 用于上传文件或内容。
DELETE: 用于请求删除资源。
HEAD: 用于获取资源的元信息，而不是资源本身。
3. 状态码
服务器对请求的响应中包含一个状态码，它表示请求的成功或失败，以及失败的原因。常见的状态码包
括：
200 OK: 请求成功。
404 Not Found: 请求的资源未找到。
500 Internal Server Error: 服务器内部错误。
301 Moved Permanently: 请求的资源已永久移动到新位置。
4. URL（统一资源定位符）
URL是Web上资源的地址。它指定了资源的位置以及用于访问资源的协议（例如，http://）。
5. HTTP头
HTTP请求和响应包含头部信息，这些信息包括元数据，如内容类型、内容长度、服务器信息、客户端信
息等。例如，
Content-Type 头部指示响应中的媒体类型（如text/html，application/json）。
6. 无状态协议
HTTP是一个无状态协议，这意味着服务器不会保留任何请求的数据（状态）。然而，通过使用如
Cookies这样的机制，可以在多个请求之间维持状态。
7. 安全性（HTTPS）
HTTPS是HTTP的安全版本，它在HTTP和TCP层之间增加了一个加密层（通常是SSL/TLS）。这提供了数
据传输的加密和更好的安全性。
8. RESTful API
 RESTful是一种使用HTTP协议的Web服务设计风格，它利用HTTP的方法来实现API的不同操作。在
RESTful架构中，每个URL代表一个资源，并使用HTTP的方法（如GET, POST）来处理这些资源。
9. Session和Cookies
由于HTTP本身是无状态的，Cookies和会话（Session）被用来在多个请求之间存储用户数据，从而为用
户提供连贯的体验。
这些概念构成了HTTP的基础，是理解和使用HTTP协议的关键。每个概念都有它的具体细节和使用场景，
了解这些有助于更好地在网络应用开发中应用HTTP。
7.7 QT的HTTP编程 
Qt中的HTTP编程主要涉及使用Qt的网络模块来进行HTTP请求和处理HTTP响应。Qt提供了一系列类来处
理网络通信，其中最常用的类是
QNetworkAccessManager 、
QNetworkRequest 、
QNetworkReply 以
及相关的支持类。
以下是一个基本的HTTP编程示例，展示了如何使用Qt发送一个简单的HTTP GET请求并处理响应：
步骤 1: 包含必要的头文件
#include <QCoreApplication>
 #include <QNetworkAccessManager>
 #include <QNetworkRequest>
 #include <QNetworkReply>
 #include <QObject>
 #include <QDebug>
步骤 2: 发送HTTP请求
创建一个
QNetworkAccessManager 对象，并使用它发送HTTP请求。
异步地处理请求，并返回一个
QNetworkReply 对象。
QNetworkAccessManager 对象会
int main(int argc, char *argv[])
{
 }
 QCoreApplication a(argc, argv);
 QNetworkAccessManager manager;
 QNetworkRequest request(QUrl("http://example.com"));
 QNetworkReply *reply = manager.get(request);
 QObject::connect(reply, &QNetworkReply::finished, [&]() {
 if (reply->error()) {
 qDebug() << "Error:" << reply->errorString();
 return;
 }
 });
 QString response = reply->readAll();
 qDebug() << "Response:" << response;
 return a.exec();
在这个例子中，我们使用
QNetworkAccessManager 的
mple.com"。然后，我们连接了
get 方法发送了一个HTTP GET请求到"
 QNetworkReply 对象的
http://exa
 finished 信号到一个lambda函数，该函数在收
到HTTP响应时被调用。
注意事项
1. 异步处理: 
QNetworkAccessManager 的请求是异步的。这意味着
应将在稍后通过信号处理。
2. 错误处理: 应该检查
get 方法会立即返回，而HTTP响
QNetworkReply 对象是否有错误，并相应地处理。
3. 内存管理: 
QNetworkReply 对象需要被正确地管理，以避免内存泄漏。通常情况下，使用
QObject::deleteLater 来安排删除它是一个好方法。
7.8 JSON数据 
7.8.1 概述 
JSON（JavaScript Object  Notation）是一种轻量级的数据交换格式。它易于人阅读和编写，同时也易于
机器解析和生成。JSON是基于JavaScript的一个子集，尽管它是独立于语言的，且有多种语言支持。
JSON常用于网络应用程序中的数据传输，尤其是在Web应用程序中与后端服务器通信。
使用JSON的原因总结如下：
原因
描述
易于阅读和编写 JSON的结构简单、清晰，对人类来说易于阅读和编写。
轻量级数据格式 相较于XML等标记语言，JSON更轻量，使用更少的符号，数据体积更小。
跨语言支持
易于解析和生成 大多数编程语言都提供了解析和生成JSON的内置支持或库。
JSON是独立于语言的，被广泛支持和使用在多种编程语言中。
原因描述
网络友好JSON格式适合Web环境，易于通过网络传输，是Web API的常用格式。
数据互操作性作为一种标准化格式，JSON提高了不同系统间的数据互操作性。
语言/平
台JSON处理库/接口特点/描述
C Jansson提供JSON的编码、解码和处理功能
C++ nlohmann/json现代C++（从C++11开始）的JSON库，易于使用
Java Jackson强大的JSON处理库，支持JSON的序列化和反序列化
 Gson Google提供的JSON序列化/反序列化库
Python json Python标准库中的JSON处理模块
Qt QJsonDocument Qt框架中用于JSON处理的类
 QJsonObject用于表示JSON对象的Qt类
 QJsonArray用于表示JSON数组的Qt类
Android org.json Android SDK自带的JSON处理类，提供基础JSON操作功能
iOS JSONSerialization Apple提供的用于JSON处理的类，部分Swift和Objective-C标
准库中
BS/CS开发过程中，会使用不同的编程语言，JSON作为数据传输的标准化格式，方便程序员协议约定和
数据处理，以下是不同编程语言处理JSON的方案
7.8.2 QT生成JSON数据 
在Qt中生成JSON数据并将其保存到文件的一个基本示例涉及使用QJsonDocument、QJsonObject和
QJsonArray类。以下是创建一个简单JSON对象并将其保存到文件的示例代码。
#include <QJsonDocument>
 #include <QJsonObject>
 #include <QJsonArray>
 #include <QFile>
 #include <QDebug>
 void createJsonFile() {
    // 创建一个JSON对象  键值对
    QJsonObject jsonObj;
    jsonObj["name"] = "John Doe";
    jsonObj["age"] = 30;
    jsonObj["email"] = "<EMAIL>";
    // 创建一个JSON数组
    QJsonArray jsonArr;
    jsonArr.append("C++");
    jsonArr.append("Python");
jsonArr.append("JavaScript");
 jsonArr.append(123);
 // 将数组添加到JSON对象
jsonObj["languages"] = jsonArr;
 // 将JSON对象转换为JSON文档
QJsonDocument jsonDoc(jsonObj);
 // 将JSON文档转换为字符串（也可以是压缩格式）
QByteArray jsonData = jsonDoc.toJson(QJsonDocument::Indented);
 // 将JSON数据写入文件
QFile file("output.json");
 if (!file.open(QIODevice::WriteOnly)) {
 qDebug() << "Failed to open file for writing";
 return;
 }
 file.write(jsonData);
 file.close();
 }
 qDebug() << "JSON data saved to output.json";
 int main() {
 createJsonFile();
 return 0;
 }
说明
1. 创建JSON对象：使用
QJsonObject 来构建JSON对象，并使用键值对填充数据。
2. 创建JSON数组：使用
QJsonArray 来创建一个数组，并添加元素。
3. 组合JSON结构：将JSON数组添加到JSON对象中。
4. 生成JSON文档：通过
QJsonDocument 来处理JSON数据，可以选择格式化（缩进）或压缩形式。
5. 保存到文件：创建
QFile 对象，打开文件，写入JSON数据，并关闭文件。
这个例子展示了Qt中处理JSON的基础流程，包括创建、填充数据、转换为字符串，以及写入文件。您可
以根据需要调整这个流程来适应更复杂的JSON结构或数据。
#include "widget.h"
 #include "ui_widget.h"
 #include <QFile>
 #include <QJsonArray>
 #include <QJsonDocument>
 #include <QJsonObject>
 Widget::Widget(QWidget *parent)
 : QWidget(parent)
 , ui(new Ui::Widget)
{
 }
 ui->setupUi(this);
 QJsonObject rootObj;
 rootObj["cityid"]  = "1010100";
 rootObj["date"]    
= "2024-01-23";
 rootObj["weather"] = "雨夹雪";
 rootObj["tmp"]     
= 3;
 QJsonArray jsonArray;
 jsonArray.append("data1");
 jsonArray.append("data2");
 jsonArray.append("data3");
 jsonArray.append(100);
 rootObj["testArry"] = jsonArray;
 QJsonDocument jsonDoc(rootObj);
 QByteArray jsonArry = jsonDoc.toJson();
 QFile file("D:/QT/test.json");
 file.open(QIODevice::WriteOnly);
 file.write(jsonArry);
 file.close();
 Widget::~Widget()
 {
 delete ui;
 }
在JSON中，数组可以包含多种类型的元素，包括对象。当您在Qt中处理JSON数组，其中的元素是对象
时，您可以使用
QJsonArray 和
QJsonObject 来创建和处理这些数据结构。以下是一个示例，展示了如
何创建一个包含多个对象的JSON数组，并将该数组添加到一个JSON对象中。
示例代码
#include "widget.h"
 #include "ui_widget.h"
 #include <QFile>
 #include <QJsonArray>
 #include <QJsonDocument>
 #include <QJsonObject>
 Widget::Widget(QWidget *parent)
 : QWidget(parent)
 , ui(new Ui::Widget)
 {
 ui->setupUi(this);
 QJsonObject rootObj;
 rootObj["cityid"]  = "1010100";
 rootObj["date"]    
= "2024-01-23";
 rootObj["weather"] = "雨夹雪";
= 3;
 rootObj["tmp"]     
//Json数组
QJsonArray jsonArray;
 jsonArray.append("data1");
 jsonArray.append("data2");
 jsonArray.append("data3");
 jsonArray.append(100);
 rootObj["testArry"] = jsonArray;
 QJsonObject alarmObj;
 alarmObj["alamType"] = "雪灾";
 alarmObj["alamLeve"] = "黄色";
 alarmObj["alamTitle"] = "福州市警告老陈多穿点衣服";
 rootObj["alam"] = alarmObj;
 QJsonObject day0;
 day0["day"] = "星期一";
 day0["wea"] = "晴";
 day0["tem"] = 5.7;
 QJsonObject day1;
 day1["day"] = "星期二";
 day1["wea"] = "晴";
 day1["tem"] = 7;
 QJsonObject day2;
 day2["day"] = "星期三";
 day2["wea"] = "多云";
 day2["tem"] = 17;
 QJsonArray dayArray;
 dayArray.append(day0);
 dayArray.append(day1);
 dayArray.append(day2);
 rootObj["days"] = dayArray;
 //通过QJsonDocument把JSON数据转换成QByteArray
 QJsonDocument jsonDoc(rootObj);
 QByteArray jsonArry = jsonDoc.toJson();
 QFile file("D:/QT/test.json");
 file.open(QIODevice::WriteOnly);
 file.write(jsonArry);
 file.close();
 }
 Widget::~Widget()
 {
 delete ui;
 }
7.8.3 QT解析JSON数据 
在Qt中解析JSON数据通常涉及到使用QJsonDocument、QJsonObject和QJsonArray类。这些类提供
了处理JSON数据的必要工具，使您能够从JSON字符串中提取信息、遍历JSON对象或数组，并访问具体
的数据项。以下是一个基本的示例，展示了如何在Qt中解析JSON字符串。
示例：解析JSON字符串
假设您有一个JSON字符串，例如：
以下是如何在Qt中解析这个JSON字符串的步骤：
{
    "name": "John Doe",
    "age": 30,
    "email": "<EMAIL>",
    "skills": ["C++", "Python", "JavaScript"]
 }
 #include <QJsonDocument>
 #include <QJsonObject>
 #include <QJsonArray>
 #include <QDebug>
 void parseJson() {
    // JSON字符串
    /*
    R 是用于定义原始字符串字面量（Raw String Literal）的标记。
    在C++中，原始字符串字面量是一种方便的语法，
    用于创建包含多行文本和特殊字符的字符串，而无需转义。
    R"("chenlichen")"
    */
    QString testStr = "chenli\"c";
    QString jsonString = R"(
        {
            "name": "John Doe",
            "age": 30,
            "email": "<EMAIL>",
            "skills": ["C++", "Python", "JavaScript"]
        }
    )";
                        /*jsonString = "{\n"
                         "    \"name\": \"John Doe\",\n"
                         "    \"age\": 30\n"
                         "}";
                         */
    // 将JSON字符串转换为QJsonDocument
    QJsonDocument jsonDoc = QJsonDocument::fromJson(jsonString.toUtf8());
    // 检查JSON文档是否包含一个对象
    if (!jsonDoc.isNull() && jsonDoc.isObject()) {
        // 获取JSON对象
        QJsonObject jsonObj = jsonDoc.object();
// 访问对象的键值
QString name = jsonObj["name"].toString();
 int age = jsonObj["age"].toInt();
 QString email = jsonObj["email"].toString();
 qDebug() << "Name:" << name;
 qDebug() << "Age:" << age;
 qDebug() << "Email:" << email;
 }
 // 处理JSON数组
if (jsonObj.contains("skills") && jsonObj["skills"].isArray()) {
 QJsonArray skillsArray = jsonObj["skills"].toArray();
 for (const QJsonValue &value : skillsArray) {
 qDebug() << "Skill:" << value.toString();
 }
 }
 } else {
 qDebug() << "Invalid JSON...";
 }
 int main() {
 parseJson();
 return 0;
 }
说明
1. 字符串转换为
QJsonDocument 
：使用
QJsonDocument 对象。
2. 提取
QJsonDocument::fromJson 方法将JSON字符串转换为
QJsonObject 
：如果
QJsonDocument 包含一个JSON对象，使用
3. 访问对象数据：使用键（如
"name" 、
"age" ）访问
object() 方法获取它。
QJsonObject 中的数据。
4. 处理数组：如果对象包含一个数组，使用
QJsonArray 来遍历数组中的元素。
这个示例提供了一个基础框架，用于在Qt中解析和处理JSON数据。您可以根据实际需要调整这个过程，
以适应不同的JSON结构和数据类型。
在Qt中，如果你想要将JSON数据解析到一个
QMap 中，你可以遍历JSON对象的所有键值对，并将它们添
加到QMap里。这个方法特别适合于当你的JSON对象是一个简单的键值对集合时。以下是一个如何实现
这一点的示例。
示例：将JSON数据解析到QMap中
假设你有以下JSON数据：
json
 {
 }
 "name": "John Doe",
 "age": "30",
 "email": "<EMAIL>"
以下是如何将这些数据解析到
QMap<QString, QString> 中的步骤：
#include <QJsonDocument>
 #include <QJsonObject>
 #include <QMap>
 #include <QDebug>
 void parseJsonToMap() {
 // JSON字符串
QString jsonString = R"(
 {
 "name": "John Doe",
 "age": "30",
 "email": "<EMAIL>"
 }
 )";
 // 将JSON字符串转换为QJsonDocument
 QJsonDocument jsonDoc = QJsonDocument::fromJson(jsonString.toUtf8());
 // 准备一个QMap来存储解析的数据
QMap<QString, QString> dataMap;
 // 解析JSON对象并填充QMap
 if (!jsonDoc.isNull() && jsonDoc.isObject()) {
 QJsonObject jsonObj = jsonDoc.object();
 for (auto key : jsonObj.keys()) {
 dataMap[key] = jsonObj.value(key).toString();
 }
 } else {
 qDebug() << "Invalid JSON...";
 }
 // 打印QMap内容
for (auto key : dataMap.keys()) {
 qDebug() << key << ":" << dataMap[key];
 }
 }
 int main() {
 parseJsonToMap();
 return 0;
 }
说明
1. 从JSON字符串创建
QJsonDocument 
：使用
2. 创建
QJsonDocument::fromJson 来解析JSON字符串。
QMap 
：定义一个
QMap<QString, QString> 来存储键值对。
3. 遍历JSON对象：使用
keys() 方法获取所有键，然后遍历这些键，将对应的值添加到
4. 打印
QMap 
内容：遍历
QMap 并打印键值对。
这个示例展示了如何将JSON对象的键值对解析到
QMap 中。
QMap 中。这种方法适用于键值对类型的简单JSON对
象。对于更复杂的JSON结构，可能需要更详细的解析逻辑。
解析如下JSON
#include "widget.h"
 #include "ui_widget.h"
 #include <QFile>
 #include <QJsonDocument>
 #include <QJsonObject>
 #include <QDebug>
 #include <QJsonArray>
 Widget::Widget(QWidget *parent)
 : QWidget(parent)
 , ui(new Ui::Widget)
 {
 ui->setupUi(this);
 //第一步：读取JSON文件保存到QByteArray中
QFile file("D:/QT/test.json");
 file.open(QIODevice::ReadOnly);
 QByteArray rawData = file.readAll();
 file.close();
 //第二步：把QByteArray转成JSONDoc
 QJsonDocument jsonDoc = QJsonDocument::fromJson(rawData);
 if( !jsonDoc.isNull() && jsonDoc.isObject()){
 //第三步：把JsonDoc转成JsonObj
 QJsonObject jsonRoot = jsonDoc.object();
 //第四步：如果解析普通键值对，通过“下表键”来获取值
QString strW      
= jsonRoot["weather"].toString();
 QString strCityId = jsonRoot["cityid"].toString();
        int tempretrue = jsonRoot["tmp"].toInt();
        qDebug() << strW;
        qDebug() << strCityId;
        qDebug() << QString::number(tempretrue);
        //第五步：判读是否是一个数组
        if(jsonRoot.contains("testArry") && jsonRoot["testArry"].isArray()){
            qDebug() << "array";
            //如果是数组，转换成JSON数组
            QJsonArray testArray = jsonRoot["testArry"].toArray();
            //遍历数组，访问每一项
            for(QJsonValue val : testArray){
                //QJsonValue的type函数返回数据类型，根据不同的数据类型处理数据
               // QJsonValue::Type t = val.type();
                switch (val.type()) {
                    case QJsonValue::Double:
                    qDebug() << QString::number(val.toDouble());
                    break;
                case QJsonValue::String:
                    qDebug() << val.toString();
                    break;
                case QJsonValue::Object:
                    break;
                }
            }
        }
        //第六步：判断某个键对应的值，是否是一个json对象
        if(jsonRoot.contains("alam") && jsonRoot["alam"].isObject()){
            //转成Json对象后处理
            QJsonObject alamObj = jsonRoot["alam"].toObject();
            qDebug() << alamObj["alamLeve"].toString();
            qDebug() << alamObj["alamTitle"].toString();
            qDebug() << alamObj["alamType"].toString();
        }
        if(jsonRoot.contains("days") && jsonRoot["days"].isArray()){
            QJsonArray dayArray = jsonRoot["days"].toArray();
            for(QJsonValue val : dayArray){
                //if(val.type() ==QJsonValue::Object ){
                if(val.isObject()){
                    QJsonObject obj = val.toObject();
                    qDebug() << obj["day"].toString();
                    qDebug() << QString::number(obj["tem"].toDouble());
                    qDebug() << obj["wea"].toString();
                }
            }
        }
    }
 }
 Widget::~Widget()
 {
    delete ui;
 }
天气类型和图标
//根据keys,设置icon的路径
mTypeMap.insert("暴雪",":/res/type/BaoXue.png");
 mTypeMap.insert("暴雨",":/res/type/BaoYu. png");
 mTypeMap.insert("暴雨到大暴雨",":/res/type/BaoYuDaoDaBaoYu.png");
 mTypeMap.insert("大暴雨",":/res/type/DaBaoYu.png");
 mTypeMap.insert("大暴雨到特大暴雨",":/res/type/DaBaoYuDaoTeDaBaoYu.png");
 mTypeMap.insert("大到暴雪",":/res/type/DaDaoBaoXue.png");
 mTypeMap.insert("大雪",":/res/type/DaXue.png");
 mTypeMap.insert("大雨",":/res/type/DaYu.png");
 mTypeMap.insert("冻雨",":/res/type/DongYu.png");
 mTypeMap.insert("多云",":/res/type/DuoYun.png");
 mTypeMap.insert("浮沉",":/res/type/FuChen.png");
 mTypeMap.insert("雷阵雨",":/res/type/LeiZhenYu.png");
 mTypeMap.insert("雷阵雨伴有冰雹",":/res/type/LeiZhenYuBanYouBingBao.png");
 mTypeMap.insert("霾",":/res/type/Mai.png");
 mTypeMap.insert("强沙尘暴",":/res/type/QiangShaChenBao.png");
 mTypeMap.insert("晴",":/res/type/Qing.png");
 mTypeMap.insert("沙尘暴",":/res/type/ShaChenBao.png");
 mTypeMap.insert("特大暴雨",":/res/type/TeDaBaoYu.png");
 mTypeMap.insert("undefined",":/res/type/undefined.png");
 mTypeMap.insert("雾",":/res/type/Wu.png");
 mTypeMap.insert("小到中雪",":/res/type/XiaoDaoZhongXue.png");
 mTypeMap.insert("小到中雨",":/res/type/XiaoDaoZhongYu.png");
 mTypeMap.insert("小雪",":/res/type/XiaoXue.png");
 mTypeMap.insert("小雨",":/res/type/XiaoYu.png");
 mTypeMap.insert("雪",":/res/type/Xue.png");
 mTypeMap.insert("扬沙",":/res/type/YangSha.png");
 mTypeMap.insert("阴",":/res/type/Yin.png");
 mTypeMap.insert("雨",":/res/type/Yu.png");
 mTypeMap.insert("雨夹雪",":/res/type/YuJiaXue.png");
 mTypeMap.insert("阵雪",":/res/type/ZhenXue.png");
 mTypeMap.insert("阵雨",":/res/type/ZhenYu.png");
 mTypeMap.insert("中到大雪",":/res/type/ZhongDaoDaXue.png");
 mTypeMap.insert("中到大雨",":/res/type/ZhongDaoDaYu.png");
 mTypeMap.insert("中雪",":/res/type/ZhongXue.png");
 mTypeMap.insert("中雨",":/res/type/ZhongYu.png")