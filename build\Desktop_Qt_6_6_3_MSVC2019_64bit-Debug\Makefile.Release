#############################################################################
# Makefile for building: weatherforecast
# Generated by qmake (3.1) (Qt 6.6.3)
# Project:  ..\..\weatherforecast.pro
# Template: app
#############################################################################

MAKEFILE      = Makefile.Release

EQ            = =

####### Compiler, tools and options

CC            = cl
CXX           = cl
DEFINES       = -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DNDEBUG -DQT_QML_DEBUG -DQT_NO_DEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB
CFLAGS        = -nologo -Zc:wchar_t -FS -Zc:strictStrings -O2 -MD -utf-8 -W3 -w44456 -w44457 -w44458 $(DEFINES)
CXXFLAGS      = -nologo -Zc:wchar_t -FS -Zc:rvalueCast -Zc:inline -Zc:strictStrings -Zc:throwingNew -permissive- -Zc:__cplusplus -Zc:externConstexpr -O2 -MD -std:c++17 -utf-8 -W3 -w34100 -w34189 -w44996 -w44456 -w44457 -w44458 -wd4577 -wd4467 -EHsc $(DEFINES)
INCPATH       = -I..\..\..\weatherforecast -I. -IE:\Qt\6.6.3\msvc2019_64\include -IE:\Qt\6.6.3\msvc2019_64\include\QtWidgets -IE:\Qt\6.6.3\msvc2019_64\include\QtGui -IE:\Qt\6.6.3\msvc2019_64\include\QtNetwork -IE:\Qt\6.6.3\msvc2019_64\include\QtCore -Irelease -I. -I/include -IE:\Qt\6.6.3\msvc2019_64\mkspecs\win32-msvc 
LINKER        = link
LFLAGS        = /NOLOGO /DYNAMICBASE /NXCOMPAT /OPT:REF /OPT:ICF /INCREMENTAL:NO /SUBSYSTEM:WINDOWS "/MANIFESTDEPENDENCY:type='win32' name='Microsoft.Windows.Common-Controls' version='6.0.0.0' publicKeyToken='6595b64144ccf1df' language='*' processorArchitecture='*'"
LIBS          = E:\Qt\6.6.3\msvc2019_64\lib\Qt6Widgets.lib E:\Qt\6.6.3\msvc2019_64\lib\Qt6Gui.lib E:\Qt\6.6.3\msvc2019_64\lib\Qt6Network.lib E:\Qt\6.6.3\msvc2019_64\lib\Qt6Core.lib E:\Qt\6.6.3\msvc2019_64\lib\Qt6EntryPoint.lib shell32.lib  
QMAKE         = E:\Qt\6.6.3\msvc2019_64\bin\qmake.exe
DEL_FILE      = del
CHK_DIR_EXISTS= if not exist
MKDIR         = mkdir
COPY          = copy /y
COPY_FILE     = copy /y
COPY_DIR      = xcopy /s /q /y /i
INSTALL_FILE  = copy /y
INSTALL_PROGRAM = copy /y
INSTALL_DIR   = xcopy /s /q /y /i
QINSTALL      = E:\Qt\6.6.3\msvc2019_64\bin\qmake.exe -install qinstall
QINSTALL_PROGRAM = E:\Qt\6.6.3\msvc2019_64\bin\qmake.exe -install qinstall -exe
DEL_FILE      = del
SYMLINK       = $(QMAKE) -install ln -f -s
DEL_DIR       = rmdir
MOVE          = move
IDC           = idc
IDL           = midl
ZIP           = zip -r -9
DEF_FILE      = 
RES_FILE      = 
SED           = $(QMAKE) -install sed
MOVE          = move

####### Output directory

OBJECTS_DIR   = release

####### Files

SOURCES       = ..\..\citycodeutils.cpp \
		..\..\main.cpp \
		..\..\widget.cpp release\qrc_res.cpp \
		release\moc_widget.cpp
OBJECTS       = release\citycodeutils.obj \
		release\main.obj \
		release\widget.obj \
		release\qrc_res.obj \
		release\moc_widget.obj

DIST          =  ..\..\citycodeutils.h \
		..\..\widget.h ..\..\citycodeutils.cpp \
		..\..\main.cpp \
		..\..\widget.cpp
QMAKE_TARGET  = weatherforecast
DESTDIR        = release\ #avoid trailing-slash linebreak
TARGET         = weatherforecast.exe
DESTDIR_TARGET = release\weatherforecast.exe

####### Implicit rules

.SUFFIXES: .c .cpp .cc .cxx

.cpp.obj:
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Fo$@ $<

.cc.obj:
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Fo$@ $<

.cxx.obj:
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Fo$@ $<

.c.obj:
	$(CC) -c $(CFLAGS) $(INCPATH) -Fo$@ $<

####### Build rules

first: all
all: Makefile.Release  release\weatherforecast.exe

release\weatherforecast.exe: E:\Qt\6.6.3\msvc2019_64\lib\Qt6Widgets.lib E:\Qt\6.6.3\msvc2019_64\lib\Qt6Gui.lib E:\Qt\6.6.3\msvc2019_64\lib\Qt6Network.lib E:\Qt\6.6.3\msvc2019_64\lib\Qt6Core.lib E:\Qt\6.6.3\msvc2019_64\lib\Qt6EntryPoint.lib ui_widget.h $(OBJECTS) 
	$(LINKER) $(LFLAGS) /MANIFEST:embed /OUT:$(DESTDIR_TARGET) @<<
release\citycodeutils.obj release\main.obj release\widget.obj release\qrc_res.obj release\moc_widget.obj
$(LIBS)
<<

qmake: FORCE
	@$(QMAKE) -o Makefile.Release ..\..\weatherforecast.pro -spec win32-msvc "CONFIG+=debug" "CONFIG+=qml_debug"

qmake_all: FORCE

dist:
	$(ZIP) weatherforecast.zip $(SOURCES) $(DIST) ..\..\weatherforecast.pro E:\Qt\6.6.3\msvc2019_64\mkspecs\features\spec_pre.prf E:\Qt\6.6.3\msvc2019_64\mkspecs\common\windows-desktop.conf E:\Qt\6.6.3\msvc2019_64\mkspecs\features\win32\windows_vulkan_sdk.prf E:\Qt\6.6.3\msvc2019_64\mkspecs\common\windows-vulkan.conf E:\Qt\6.6.3\msvc2019_64\mkspecs\common\msvc-desktop.conf E:\Qt\6.6.3\msvc2019_64\mkspecs\qconfig.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_ext_freetype.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_ext_libjpeg.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_ext_libpng.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_3danimation.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_3danimation_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_3dcore.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_3dcore_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_3dextras.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_3dextras_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_3dinput.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_3dinput_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_3dlogic.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_3dlogic_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_3dquick.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_3dquick_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_3dquickanimation.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_3dquickanimation_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_3dquickextras.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_3dquickextras_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_3dquickinput.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_3dquickinput_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_3dquickrender.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_3dquickrender_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_3dquickscene2d.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_3dquickscene2d_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_3drender.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_3drender_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_activeqt.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_activeqt_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_axbase_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_axcontainer.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_axcontainer_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_axserver.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_axserver_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_bluetooth.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_bluetooth_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_bodymovin_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_charts.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_charts_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_chartsqml.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_chartsqml_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_concurrent.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_concurrent_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_core.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_core5compat.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_core5compat_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_core_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_datavisualization.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_datavisualization_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_datavisualizationqml.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_datavisualizationqml_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_dbus.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_dbus_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_designer.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_designer_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_designercomponents_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_devicediscovery_support_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_entrypoint_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_example_icons_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_fb_support_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_freetype_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_graphs.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_graphs_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_grpc.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_grpc_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_gui.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_gui_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_harfbuzz_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_help.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_help_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_httpserver.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_httpserver_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_insighttracker.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_insighttracker_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_insighttrackerqml.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_insighttrackerqml_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_jpeg_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_jsonrpc_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_labsanimation.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_labsanimation_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_labsfolderlistmodel.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_labsfolderlistmodel_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_labsqmlmodels.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_labsqmlmodels_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_labssettings.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_labssettings_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_labssharedimage.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_labssharedimage_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_labswavefrontmesh.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_labswavefrontmesh_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_languageserver_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_linguist.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_linguist_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_location.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_location_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_multimedia.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_multimedia_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_multimediaquick_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_multimediawidgets.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_multimediawidgets_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_network.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_network_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_networkauth.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_networkauth_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_nfc.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_nfc_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_opengl.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_opengl_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_openglwidgets.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_openglwidgets_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_packetprotocol_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_pdf.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_pdf_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_pdfquick.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_pdfquick_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_pdfwidgets.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_pdfwidgets_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_png_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_positioning.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_positioning_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_positioningquick.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_positioningquick_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_printsupport.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_printsupport_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_protobuf.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_protobuf_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_protobufqtcoretypes.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_protobufqtcoretypes_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_protobufqtguitypes.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_protobufqtguitypes_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_protobufwellknowntypes.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_protobufwellknowntypes_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_qdoccatch_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_qdoccatchconversionsprivate.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_qdoccatchconversionsprivate_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_qdoccatchgeneratorsprivate.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_qdoccatchgeneratorsprivate_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_qml.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_qml_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_qmlcompiler.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_qmlcompiler_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_qmlcore.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_qmlcore_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_qmldebug_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_qmldom_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_qmlintegration.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_qmlintegration_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_qmllocalstorage.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_qmllocalstorage_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_qmlls_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_qmlmodels.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_qmlmodels_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_qmltest.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_qmltest_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_qmltoolingsettings_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_qmltyperegistrar_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_qmlworkerscript.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_qmlworkerscript_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_qmlxmllistmodel.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_qmlxmllistmodel_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_quick.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_quick3d.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_quick3d_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dassetimport.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dassetimport_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dassetutils.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dassetutils_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_quick3deffects.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_quick3deffects_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dglslparser_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dhelpers.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dhelpers_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dhelpersimpl.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dhelpersimpl_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_quick3diblbaker.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_quick3diblbaker_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dparticleeffects.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dparticleeffects_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dparticles.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dparticles_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dphysics.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dphysics_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dphysicshelpers.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dphysicshelpers_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_quick3druntimerender.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_quick3druntimerender_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dspatialaudio_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dutils.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dutils_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_quick_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_quickcontrols2.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_quickcontrols2_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_quickcontrols2basic.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_quickcontrols2basic_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_quickcontrols2basicstyleimpl.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_quickcontrols2basicstyleimpl_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_quickcontrols2fusion.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_quickcontrols2fusion_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_quickcontrols2fusionstyleimpl.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_quickcontrols2fusionstyleimpl_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_quickcontrols2imagine.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_quickcontrols2imagine_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_quickcontrols2imaginestyleimpl.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_quickcontrols2imaginestyleimpl_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_quickcontrols2impl.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_quickcontrols2impl_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_quickcontrols2material.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_quickcontrols2material_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_quickcontrols2materialstyleimpl.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_quickcontrols2materialstyleimpl_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_quickcontrols2universal.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_quickcontrols2universal_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_quickcontrols2universalstyleimpl.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_quickcontrols2universalstyleimpl_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_quickcontrolstestutilsprivate_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_quickdialogs2.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_quickdialogs2_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_quickdialogs2quickimpl.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_quickdialogs2quickimpl_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_quickdialogs2utils.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_quickdialogs2utils_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_quickeffects_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_quicklayouts.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_quicklayouts_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_quickparticles_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_quickshapes_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_quicktemplates2.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_quicktemplates2_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_quicktestutilsprivate_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_quicktimeline.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_quicktimeline_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_quickwidgets.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_quickwidgets_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_remoteobjects.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_remoteobjects_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_remoteobjectsqml.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_remoteobjectsqml_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_repparser.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_repparser_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_scxml.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_scxml_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_scxmlqml.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_scxmlqml_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_sensors.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_sensors_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_sensorsquick.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_sensorsquick_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_serialbus.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_serialbus_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_serialport.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_serialport_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_shadertools.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_shadertools_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_spatialaudio.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_spatialaudio_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_sql.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_sql_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_statemachine.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_statemachine_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_statemachineqml.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_statemachineqml_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_svg.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_svg_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_svgwidgets.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_svgwidgets_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_testlib.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_testlib_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_texttospeech.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_texttospeech_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_tools_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_uiplugin.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_uitools.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_uitools_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_virtualkeyboard.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_virtualkeyboard_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_virtualkeyboardsettings.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_virtualkeyboardsettings_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_webchannel.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_webchannel_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_webchannelquick.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_webchannelquick_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_webenginecore.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_webenginecore_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_webenginequick.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_webenginequick_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_webenginequickdelegatesqml.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_webenginequickdelegatesqml_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_webenginewidgets.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_webenginewidgets_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_websockets.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_websockets_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_webview.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_webview_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_webviewquick.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_webviewquick_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_widgets.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_widgets_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_xml.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_xml_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\modules\qt_lib_zlib_private.pri E:\Qt\6.6.3\msvc2019_64\mkspecs\features\qt_functions.prf E:\Qt\6.6.3\msvc2019_64\mkspecs\features\qt_config.prf E:\Qt\6.6.3\msvc2019_64\mkspecs\win32-msvc\qmake.conf E:\Qt\6.6.3\msvc2019_64\mkspecs\features\spec_post.prf ..\..\.qmake.stash E:\Qt\6.6.3\msvc2019_64\mkspecs\features\exclusive_builds.prf E:\Qt\6.6.3\msvc2019_64\mkspecs\common\msvc-version.conf E:\Qt\6.6.3\msvc2019_64\mkspecs\features\toolchain.prf E:\Qt\6.6.3\msvc2019_64\mkspecs\features\default_pre.prf E:\Qt\6.6.3\msvc2019_64\mkspecs\features\win32\default_pre.prf E:\Qt\6.6.3\msvc2019_64\mkspecs\features\resolve_config.prf E:\Qt\6.6.3\msvc2019_64\mkspecs\features\exclusive_builds_post.prf E:\Qt\6.6.3\msvc2019_64\mkspecs\features\default_post.prf E:\Qt\6.6.3\msvc2019_64\mkspecs\features\build_pass.prf E:\Qt\6.6.3\msvc2019_64\mkspecs\features\qml_debug.prf E:\Qt\6.6.3\msvc2019_64\mkspecs\features\precompile_header.prf E:\Qt\6.6.3\msvc2019_64\mkspecs\features\warn_on.prf E:\Qt\6.6.3\msvc2019_64\mkspecs\features\permissions.prf E:\Qt\6.6.3\msvc2019_64\mkspecs\features\qt.prf E:\Qt\6.6.3\msvc2019_64\mkspecs\features\resources_functions.prf E:\Qt\6.6.3\msvc2019_64\mkspecs\features\resources.prf E:\Qt\6.6.3\msvc2019_64\mkspecs\features\moc.prf E:\Qt\6.6.3\msvc2019_64\mkspecs\features\win32\opengl.prf E:\Qt\6.6.3\msvc2019_64\mkspecs\features\uic.prf E:\Qt\6.6.3\msvc2019_64\mkspecs\features\qmake_use.prf E:\Qt\6.6.3\msvc2019_64\mkspecs\features\file_copies.prf E:\Qt\6.6.3\msvc2019_64\mkspecs\features\win32\windows.prf E:\Qt\6.6.3\msvc2019_64\mkspecs\features\testcase_targets.prf E:\Qt\6.6.3\msvc2019_64\mkspecs\features\exceptions.prf E:\Qt\6.6.3\msvc2019_64\mkspecs\features\yacc.prf E:\Qt\6.6.3\msvc2019_64\mkspecs\features\lex.prf ..\..\weatherforecast.pro ..\..\res.qrc E:\Qt\6.6.3\msvc2019_64\lib\Qt6Widgets.prl E:\Qt\6.6.3\msvc2019_64\lib\Qt6Gui.prl E:\Qt\6.6.3\msvc2019_64\lib\Qt6Network.prl E:\Qt\6.6.3\msvc2019_64\lib\Qt6Core.prl E:\Qt\6.6.3\msvc2019_64\lib\Qt6EntryPoint.prl   ..\..\res.qrc E:\Qt\6.6.3\msvc2019_64\mkspecs\features\data\dummy.cpp ..\..\citycodeutils.h ..\..\widget.h  ..\..\citycodeutils.cpp ..\..\main.cpp ..\..\widget.cpp ..\..\widget.ui    

clean: compiler_clean 
	-$(DEL_FILE) release\citycodeutils.obj release\main.obj release\widget.obj release\qrc_res.obj release\moc_widget.obj

distclean: clean 
	-$(DEL_FILE) $(DESTDIR_TARGET)
	-$(DEL_FILE) Makefile.Release

mocclean: compiler_moc_header_clean compiler_moc_objc_header_clean compiler_moc_source_clean

mocables: compiler_moc_header_make_all compiler_moc_objc_header_make_all compiler_moc_source_make_all

check: first

benchmark: first

compiler_no_pch_compiler_make_all:
compiler_no_pch_compiler_clean:
compiler_rcc_make_all: release\qrc_res.cpp
compiler_rcc_clean:
	-$(DEL_FILE) release\qrc_res.cpp
release\qrc_res.cpp: ..\..\res.qrc \
		E:\Qt\6.6.3\msvc2019_64\bin\rcc.exe \
		..\..\res\2.png \
		..\..\res\leizhenyubanyoubingbao.png \
		..\..\res\zhongxue.png \
		..\..\res\3.png \
		..\..\res\4.png \
		..\..\res\yin.png \
		..\..\res\baoyu.png \
		..\..\res\dayu.png \
		..\..\res\DaBaoYuDaoTeDaBaoYu.png \
		..\..\res\1.png \
		..\..\res\search.png \
		..\..\res\xiaodaozhongyu.png \
		..\..\res\dongyu.png \
		..\..\res\sun.png \
		..\..\res\leizhenyu.png \
		..\..\res\zhongdaodayu.png \
		..\..\res\yujiaxue.png \
		..\..\res\qiangshachenbao.png \
		..\..\res\zhenxue.png \
		..\..\res\dadaobaoxue.png \
		..\..\res\zhongdaodaxue.png \
		..\..\res\daxue.png \
		..\..\res\duoyun.png \
		..\..\res\tedabaoyu.png \
		..\..\res\xue.png \
		..\..\res\yangchen.png \
		..\..\res\zhongyu.png \
		..\..\res\wu.png \
		..\..\res\xiaoxue.png \
		..\..\res\data.json \
		..\..\res\fuchen.png \
		..\..\res\baoxue.png \
		..\..\res\zhenyu.png \
		..\..\res\dabaoyu.png \
		..\..\res\xiaoyu.png \
		..\..\res\zhonxue.png \
		..\..\res\xiaodaozhonxue.png \
		..\..\res\mai.png \
		..\..\res\close.png \
		..\..\res\leibaoyu.png \
		..\..\res\shachenbao.png
	E:\Qt\6.6.3\msvc2019_64\bin\rcc.exe -name res --no-zstd ..\..\res.qrc -o release\qrc_res.cpp

compiler_moc_predefs_make_all: release\moc_predefs.h
compiler_moc_predefs_clean:
	-$(DEL_FILE) release\moc_predefs.h
release\moc_predefs.h: E:\Qt\6.6.3\msvc2019_64\mkspecs\features\data\dummy.cpp
	cl -BxE:\Qt\6.6.3\msvc2019_64\bin\qmake.exe -nologo -Zc:wchar_t -FS -Zc:rvalueCast -Zc:inline -Zc:strictStrings -Zc:throwingNew -permissive- -Zc:__cplusplus -Zc:externConstexpr -O2 -MD -std:c++17 -utf-8 -W3 -w34100 -w34189 -w44996 -w44456 -w44457 -w44458 -wd4577 -wd4467 -E E:\Qt\6.6.3\msvc2019_64\mkspecs\features\data\dummy.cpp 2>NUL >release\moc_predefs.h

compiler_moc_header_make_all: release\moc_widget.cpp
compiler_moc_header_clean:
	-$(DEL_FILE) release\moc_widget.cpp
release\moc_widget.cpp: ..\..\widget.h \
		..\..\citycodeutils.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\QString \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qstring.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qchar.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qglobal.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qtversionchecks.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qtconfiginclude.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qconfig.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qtcore-config.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qtconfigmacros.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qtcoreexports.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qcompilerdetection.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qsystemdetection.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qprocessordetection.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qtdeprecationmarkers.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qtpreprocessorsupport.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qassert.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qtnoop.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qtypes.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qtversion.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qtclasshelpermacros.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qtypeinfo.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qcontainerfwd.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qsysinfo.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qlogging.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qflags.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qcompare_impl.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qatomic.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qbasicatomic.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qatomic_cxx11.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qgenericatomic.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qconstructormacros.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qdarwinhelpers.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qexceptionhandling.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qforeach.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qttypetraits.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qfunctionpointer.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qglobalstatic.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qmalloc.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qminmax.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qnumeric.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qoverload.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qswap.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qtenvironmentvariables.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qtresource.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qttranslation.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qversiontagging.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qstringview.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qbytearray.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qrefcount.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qnamespace.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qtmetamacros.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qarraydata.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qpair.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qarraydatapointer.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qarraydataops.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qcontainertools_impl.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qxptype_traits.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\q20functional.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\q20memory.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qbytearrayalgorithms.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qbytearrayview.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qstringfwd.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\q20type_traits.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qstringliteral.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qstringalgorithms.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qlatin1stringview.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qanystringview.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qutf8stringview.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qstringtokenizer.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qstringbuilder.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\QMap \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qmap.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qiterator.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qlist.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qhashfunctions.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qbytearraylist.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qstringlist.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qalgorithms.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qstringmatcher.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qshareddata.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qshareddata_impl.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtWidgets\QWidget \
		E:\Qt\6.6.3\msvc2019_64\include\QtWidgets\qwidget.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtWidgets\qtwidgetsglobal.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qtguiglobal.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qtgui-config.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qtguiexports.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtWidgets\qtwidgets-config.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtWidgets\qtwidgetsexports.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qwindowdefs.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qobjectdefs.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qobjectdefs_impl.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qfunctionaltools_impl.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qwindowdefs_win.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qobject.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qcoreevent.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qscopedpointer.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qmetatype.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qcompare.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qdatastream.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qiodevicebase.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qfloat16.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qmath.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qiterable.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qmetacontainer.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qcontainerinfo.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qtaggedpointer.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qscopeguard.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qobject_impl.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qbindingstorage.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qmargins.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\q23utility.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qaction.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qkeysequence.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qicon.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qsize.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qpixmap.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qpaintdevice.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qrect.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qpoint.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qcolor.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qrgb.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qrgba64.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qimage.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qpixelformat.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qtransform.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qpolygon.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qregion.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qline.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qvariant.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qdebug.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qtextstream.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qstringconverter_base.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qcontiguouscache.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qsharedpointer.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qsharedpointer_impl.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qset.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qhash.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qvarlengtharray.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qpalette.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qbrush.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qfont.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qfontmetrics.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qfontinfo.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtWidgets\qsizepolicy.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qcursor.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qbitmap.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qevent.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qiodevice.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qpointer.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qurl.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qeventpoint.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qvector2d.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qvectornd.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qpointingdevice.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qinputdevice.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qscreen.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\QList \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\QObject \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\QRect \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\QSize \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\QSizeF \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\QTransform \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qnativeinterface.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\QMouseEvent \
		E:\Qt\6.6.3\msvc2019_64\include\QtWidgets\QMenu \
		E:\Qt\6.6.3\msvc2019_64\include\QtWidgets\qmenu.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtNetwork\QNetworkAccessManager \
		E:\Qt\6.6.3\msvc2019_64\include\QtNetwork\qnetworkaccessmanager.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtNetwork\qtnetworkglobal.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtNetwork\qtnetwork-config.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtNetwork\qtnetworkexports.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtNetwork\qnetworkrequest.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\QSharedDataPointer \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\QUrl \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\QVariant \
		E:\Qt\6.6.3\msvc2019_64\include\QtNetwork\QSslConfiguration \
		E:\Qt\6.6.3\msvc2019_64\include\QtNetwork\qsslconfiguration.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtNetwork\qsslsocket.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtNetwork\qtcpsocket.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtNetwork\qabstractsocket.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtNetwork\qhostaddress.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtNetwork\qsslerror.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtNetwork\qsslcertificate.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qcryptographichash.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qdatetime.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qcalendar.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qlocale.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtNetwork\qssl.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\QFlags \
		E:\Qt\6.6.3\msvc2019_64\include\QtNetwork\QSslPreSharedKeyAuthenticator \
		E:\Qt\6.6.3\msvc2019_64\include\QtNetwork\qsslpresharedkeyauthenticator.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\QMetaType \
		E:\Qt\6.6.3\msvc2019_64\include\QtNetwork\QNetworkRequest \
		E:\Qt\6.6.3\msvc2019_64\include\QtNetwork\QNetworkReply \
		E:\Qt\6.6.3\msvc2019_64\include\QtNetwork\qnetworkreply.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\QIODevice \
		E:\Qt\6.6.3\msvc2019_64\include\QtWidgets\QMessageBox \
		E:\Qt\6.6.3\msvc2019_64\include\QtWidgets\qmessagebox.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtWidgets\qdialog.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtWidgets\qdialogbuttonbox.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\QJsonDocument \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qjsondocument.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qjsonvalue.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qcborvalue.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qcborcommon.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qregularexpression.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\quuid.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qendian.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\QJsonObject \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qjsonobject.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\QJsonArray \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qjsonarray.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\QFile \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qfile.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qfiledevice.h \
		release\moc_predefs.h \
		E:\Qt\6.6.3\msvc2019_64\bin\moc.exe
	E:\Qt\6.6.3\msvc2019_64\bin\moc.exe $(DEFINES) --compiler-flavor=msvc --include "C:/Users/<USER>/Downloads/weatherforecast (2)/weatherforecast/build/Desktop_Qt_6_6_3_MSVC2019_64bit-Debug/release/moc_predefs.h" -IE:/Qt/6.6.3/msvc2019_64/mkspecs/win32-msvc -I"C:/Users/<USER>/Downloads/weatherforecast (2)/weatherforecast" -IE:/Qt/6.6.3/msvc2019_64/include -IE:/Qt/6.6.3/msvc2019_64/include/QtWidgets -IE:/Qt/6.6.3/msvc2019_64/include/QtGui -IE:/Qt/6.6.3/msvc2019_64/include/QtNetwork -IE:/Qt/6.6.3/msvc2019_64/include/QtCore -I. -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.39.33519\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.39.33519\ATLMFC\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" ..\..\widget.h -o release\moc_widget.cpp

compiler_moc_objc_header_make_all:
compiler_moc_objc_header_clean:
compiler_moc_source_make_all:
compiler_moc_source_clean:
compiler_uic_make_all: ui_widget.h
compiler_uic_clean:
	-$(DEL_FILE) ui_widget.h
ui_widget.h: ..\..\widget.ui \
		E:\Qt\6.6.3\msvc2019_64\bin\uic.exe
	E:\Qt\6.6.3\msvc2019_64\bin\uic.exe ..\..\widget.ui -o ui_widget.h

compiler_yacc_decl_make_all:
compiler_yacc_decl_clean:
compiler_yacc_impl_make_all:
compiler_yacc_impl_clean:
compiler_lex_make_all:
compiler_lex_clean:
compiler_clean: compiler_rcc_clean compiler_moc_predefs_clean compiler_moc_header_clean compiler_uic_clean 



####### Compile

release\citycodeutils.obj: ..\..\citycodeutils.cpp ..\..\citycodeutils.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\QString \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qstring.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qchar.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qglobal.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qtversionchecks.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qtconfiginclude.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qconfig.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qtcore-config.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qtconfigmacros.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qtcoreexports.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qcompilerdetection.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qsystemdetection.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qprocessordetection.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qtdeprecationmarkers.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qtpreprocessorsupport.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qassert.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qtnoop.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qtypes.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qtversion.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qtclasshelpermacros.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qtypeinfo.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qcontainerfwd.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qsysinfo.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qlogging.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qflags.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qcompare_impl.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qatomic.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qbasicatomic.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qatomic_cxx11.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qgenericatomic.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qconstructormacros.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qdarwinhelpers.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qexceptionhandling.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qforeach.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qttypetraits.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qfunctionpointer.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qglobalstatic.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qmalloc.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qminmax.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qnumeric.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qoverload.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qswap.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qtenvironmentvariables.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qtresource.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qttranslation.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qversiontagging.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qstringview.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qbytearray.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qrefcount.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qnamespace.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qtmetamacros.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qarraydata.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qpair.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qarraydatapointer.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qarraydataops.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qcontainertools_impl.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qxptype_traits.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\q20functional.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\q20memory.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qbytearrayalgorithms.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qbytearrayview.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qstringfwd.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\q20type_traits.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qstringliteral.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qstringalgorithms.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qlatin1stringview.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qanystringview.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qutf8stringview.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qstringtokenizer.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qstringbuilder.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\QMap \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qmap.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qiterator.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qlist.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qhashfunctions.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qbytearraylist.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qstringlist.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qalgorithms.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qstringmatcher.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qshareddata.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qshareddata_impl.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\QJsonDocument \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qjsondocument.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qjsonvalue.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qcborvalue.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qdatetime.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qcalendar.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qlocale.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qvariant.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qmetatype.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qcompare.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qdatastream.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qscopedpointer.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qiodevicebase.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qfloat16.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qmath.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qiterable.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qmetacontainer.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qcontainerinfo.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qtaggedpointer.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qobjectdefs.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qobjectdefs_impl.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qfunctionaltools_impl.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qscopeguard.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qdebug.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qtextstream.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qstringconverter_base.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qcontiguouscache.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qsharedpointer.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qsharedpointer_impl.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qset.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qhash.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qvarlengtharray.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\q23utility.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qobject.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qcoreevent.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qobject_impl.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qbindingstorage.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qcborcommon.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qregularexpression.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qurl.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\quuid.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qendian.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\QFile \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qfile.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qfiledevice.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qiodevice.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\QJsonArray \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qjsonarray.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Forelease\citycodeutils.obj ..\..\citycodeutils.cpp

release\main.obj: ..\..\main.cpp ..\..\widget.h \
		..\..\citycodeutils.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\QString \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qstring.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qchar.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qglobal.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qtversionchecks.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qtconfiginclude.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qconfig.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qtcore-config.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qtconfigmacros.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qtcoreexports.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qcompilerdetection.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qsystemdetection.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qprocessordetection.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qtdeprecationmarkers.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qtpreprocessorsupport.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qassert.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qtnoop.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qtypes.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qtversion.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qtclasshelpermacros.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qtypeinfo.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qcontainerfwd.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qsysinfo.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qlogging.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qflags.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qcompare_impl.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qatomic.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qbasicatomic.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qatomic_cxx11.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qgenericatomic.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qconstructormacros.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qdarwinhelpers.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qexceptionhandling.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qforeach.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qttypetraits.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qfunctionpointer.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qglobalstatic.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qmalloc.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qminmax.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qnumeric.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qoverload.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qswap.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qtenvironmentvariables.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qtresource.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qttranslation.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qversiontagging.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qstringview.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qbytearray.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qrefcount.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qnamespace.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qtmetamacros.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qarraydata.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qpair.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qarraydatapointer.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qarraydataops.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qcontainertools_impl.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qxptype_traits.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\q20functional.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\q20memory.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qbytearrayalgorithms.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qbytearrayview.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qstringfwd.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\q20type_traits.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qstringliteral.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qstringalgorithms.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qlatin1stringview.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qanystringview.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qutf8stringview.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qstringtokenizer.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qstringbuilder.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\QMap \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qmap.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qiterator.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qlist.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qhashfunctions.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qbytearraylist.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qstringlist.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qalgorithms.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qstringmatcher.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qshareddata.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qshareddata_impl.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtWidgets\QWidget \
		E:\Qt\6.6.3\msvc2019_64\include\QtWidgets\qwidget.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtWidgets\qtwidgetsglobal.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qtguiglobal.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qtgui-config.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qtguiexports.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtWidgets\qtwidgets-config.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtWidgets\qtwidgetsexports.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qwindowdefs.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qobjectdefs.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qobjectdefs_impl.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qfunctionaltools_impl.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qwindowdefs_win.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qobject.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qcoreevent.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qscopedpointer.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qmetatype.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qcompare.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qdatastream.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qiodevicebase.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qfloat16.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qmath.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qiterable.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qmetacontainer.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qcontainerinfo.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qtaggedpointer.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qscopeguard.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qobject_impl.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qbindingstorage.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qmargins.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\q23utility.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qaction.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qkeysequence.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qicon.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qsize.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qpixmap.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qpaintdevice.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qrect.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qpoint.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qcolor.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qrgb.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qrgba64.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qimage.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qpixelformat.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qtransform.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qpolygon.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qregion.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qline.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qvariant.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qdebug.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qtextstream.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qstringconverter_base.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qcontiguouscache.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qsharedpointer.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qsharedpointer_impl.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qset.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qhash.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qvarlengtharray.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qpalette.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qbrush.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qfont.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qfontmetrics.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qfontinfo.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtWidgets\qsizepolicy.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qcursor.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qbitmap.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qevent.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qiodevice.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qpointer.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qurl.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qeventpoint.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qvector2d.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qvectornd.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qpointingdevice.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qinputdevice.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qscreen.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\QList \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\QObject \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\QRect \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\QSize \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\QSizeF \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\QTransform \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qnativeinterface.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\QMouseEvent \
		E:\Qt\6.6.3\msvc2019_64\include\QtWidgets\QMenu \
		E:\Qt\6.6.3\msvc2019_64\include\QtWidgets\qmenu.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtNetwork\QNetworkAccessManager \
		E:\Qt\6.6.3\msvc2019_64\include\QtNetwork\qnetworkaccessmanager.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtNetwork\qtnetworkglobal.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtNetwork\qtnetwork-config.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtNetwork\qtnetworkexports.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtNetwork\qnetworkrequest.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\QSharedDataPointer \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\QUrl \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\QVariant \
		E:\Qt\6.6.3\msvc2019_64\include\QtNetwork\QSslConfiguration \
		E:\Qt\6.6.3\msvc2019_64\include\QtNetwork\qsslconfiguration.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtNetwork\qsslsocket.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtNetwork\qtcpsocket.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtNetwork\qabstractsocket.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtNetwork\qhostaddress.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtNetwork\qsslerror.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtNetwork\qsslcertificate.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qcryptographichash.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qdatetime.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qcalendar.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qlocale.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtNetwork\qssl.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\QFlags \
		E:\Qt\6.6.3\msvc2019_64\include\QtNetwork\QSslPreSharedKeyAuthenticator \
		E:\Qt\6.6.3\msvc2019_64\include\QtNetwork\qsslpresharedkeyauthenticator.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\QMetaType \
		E:\Qt\6.6.3\msvc2019_64\include\QtNetwork\QNetworkRequest \
		E:\Qt\6.6.3\msvc2019_64\include\QtNetwork\QNetworkReply \
		E:\Qt\6.6.3\msvc2019_64\include\QtNetwork\qnetworkreply.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\QIODevice \
		E:\Qt\6.6.3\msvc2019_64\include\QtWidgets\QMessageBox \
		E:\Qt\6.6.3\msvc2019_64\include\QtWidgets\qmessagebox.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtWidgets\qdialog.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtWidgets\qdialogbuttonbox.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\QJsonDocument \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qjsondocument.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qjsonvalue.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qcborvalue.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qcborcommon.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qregularexpression.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\quuid.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qendian.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\QJsonObject \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qjsonobject.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\QJsonArray \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qjsonarray.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\QFile \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qfile.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qfiledevice.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtWidgets\QApplication \
		E:\Qt\6.6.3\msvc2019_64\include\QtWidgets\qapplication.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qcoreapplication.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qeventloop.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qcoreapplication_platform.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qfuture.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qfutureinterface.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qmutex.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qdeadlinetimer.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qelapsedtimer.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qtsan_impl.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qresultstore.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qfuture_impl.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qthreadpool.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qthread.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qrunnable.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qexception.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qpromise.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qguiapplication.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qinputmethod.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qguiapplication_platform.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Forelease\main.obj ..\..\main.cpp

release\widget.obj: ..\..\widget.cpp ..\..\widget.h \
		..\..\citycodeutils.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\QString \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qstring.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qchar.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qglobal.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qtversionchecks.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qtconfiginclude.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qconfig.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qtcore-config.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qtconfigmacros.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qtcoreexports.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qcompilerdetection.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qsystemdetection.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qprocessordetection.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qtdeprecationmarkers.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qtpreprocessorsupport.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qassert.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qtnoop.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qtypes.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qtversion.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qtclasshelpermacros.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qtypeinfo.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qcontainerfwd.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qsysinfo.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qlogging.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qflags.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qcompare_impl.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qatomic.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qbasicatomic.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qatomic_cxx11.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qgenericatomic.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qconstructormacros.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qdarwinhelpers.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qexceptionhandling.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qforeach.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qttypetraits.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qfunctionpointer.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qglobalstatic.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qmalloc.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qminmax.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qnumeric.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qoverload.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qswap.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qtenvironmentvariables.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qtresource.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qttranslation.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qversiontagging.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qstringview.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qbytearray.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qrefcount.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qnamespace.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qtmetamacros.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qarraydata.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qpair.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qarraydatapointer.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qarraydataops.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qcontainertools_impl.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qxptype_traits.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\q20functional.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\q20memory.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qbytearrayalgorithms.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qbytearrayview.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qstringfwd.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\q20type_traits.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qstringliteral.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qstringalgorithms.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qlatin1stringview.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qanystringview.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qutf8stringview.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qstringtokenizer.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qstringbuilder.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\QMap \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qmap.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qiterator.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qlist.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qhashfunctions.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qbytearraylist.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qstringlist.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qalgorithms.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qstringmatcher.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qshareddata.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qshareddata_impl.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtWidgets\QWidget \
		E:\Qt\6.6.3\msvc2019_64\include\QtWidgets\qwidget.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtWidgets\qtwidgetsglobal.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qtguiglobal.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qtgui-config.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qtguiexports.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtWidgets\qtwidgets-config.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtWidgets\qtwidgetsexports.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qwindowdefs.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qobjectdefs.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qobjectdefs_impl.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qfunctionaltools_impl.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qwindowdefs_win.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qobject.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qcoreevent.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qscopedpointer.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qmetatype.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qcompare.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qdatastream.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qiodevicebase.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qfloat16.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qmath.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qiterable.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qmetacontainer.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qcontainerinfo.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qtaggedpointer.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qscopeguard.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qobject_impl.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qbindingstorage.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qmargins.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\q23utility.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qaction.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qkeysequence.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qicon.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qsize.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qpixmap.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qpaintdevice.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qrect.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qpoint.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qcolor.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qrgb.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qrgba64.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qimage.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qpixelformat.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qtransform.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qpolygon.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qregion.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qline.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qvariant.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qdebug.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qtextstream.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qstringconverter_base.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qcontiguouscache.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qsharedpointer.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qsharedpointer_impl.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qset.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qhash.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qvarlengtharray.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qpalette.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qbrush.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qfont.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qfontmetrics.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qfontinfo.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtWidgets\qsizepolicy.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qcursor.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qbitmap.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qevent.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qiodevice.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qpointer.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qurl.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qeventpoint.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qvector2d.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qvectornd.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qpointingdevice.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qinputdevice.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qscreen.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\QList \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\QObject \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\QRect \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\QSize \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\QSizeF \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\QTransform \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qnativeinterface.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\QMouseEvent \
		E:\Qt\6.6.3\msvc2019_64\include\QtWidgets\QMenu \
		E:\Qt\6.6.3\msvc2019_64\include\QtWidgets\qmenu.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtNetwork\QNetworkAccessManager \
		E:\Qt\6.6.3\msvc2019_64\include\QtNetwork\qnetworkaccessmanager.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtNetwork\qtnetworkglobal.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtNetwork\qtnetwork-config.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtNetwork\qtnetworkexports.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtNetwork\qnetworkrequest.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\QSharedDataPointer \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\QUrl \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\QVariant \
		E:\Qt\6.6.3\msvc2019_64\include\QtNetwork\QSslConfiguration \
		E:\Qt\6.6.3\msvc2019_64\include\QtNetwork\qsslconfiguration.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtNetwork\qsslsocket.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtNetwork\qtcpsocket.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtNetwork\qabstractsocket.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtNetwork\qhostaddress.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtNetwork\qsslerror.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtNetwork\qsslcertificate.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qcryptographichash.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qdatetime.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qcalendar.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qlocale.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtNetwork\qssl.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\QFlags \
		E:\Qt\6.6.3\msvc2019_64\include\QtNetwork\QSslPreSharedKeyAuthenticator \
		E:\Qt\6.6.3\msvc2019_64\include\QtNetwork\qsslpresharedkeyauthenticator.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\QMetaType \
		E:\Qt\6.6.3\msvc2019_64\include\QtNetwork\QNetworkRequest \
		E:\Qt\6.6.3\msvc2019_64\include\QtNetwork\QNetworkReply \
		E:\Qt\6.6.3\msvc2019_64\include\QtNetwork\qnetworkreply.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\QIODevice \
		E:\Qt\6.6.3\msvc2019_64\include\QtWidgets\QMessageBox \
		E:\Qt\6.6.3\msvc2019_64\include\QtWidgets\qmessagebox.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtWidgets\qdialog.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtWidgets\qdialogbuttonbox.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\QJsonDocument \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qjsondocument.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qjsonvalue.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qcborvalue.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qcborcommon.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qregularexpression.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\quuid.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qendian.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\QJsonObject \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qjsonobject.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\QJsonArray \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qjsonarray.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\QFile \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qfile.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qfiledevice.h \
		..\..\ui_widget.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\QIcon \
		E:\Qt\6.6.3\msvc2019_64\include\QtWidgets\QApplication \
		E:\Qt\6.6.3\msvc2019_64\include\QtWidgets\qapplication.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qcoreapplication.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qeventloop.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qcoreapplication_platform.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qfuture.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qfutureinterface.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qmutex.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qdeadlinetimer.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qelapsedtimer.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qtsan_impl.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qresultstore.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qfuture_impl.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qthreadpool.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qthread.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qrunnable.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qexception.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtCore\qpromise.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qguiapplication.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qinputmethod.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qguiapplication_platform.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtWidgets\QGridLayout \
		E:\Qt\6.6.3\msvc2019_64\include\QtWidgets\qgridlayout.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtWidgets\qlayout.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtWidgets\qlayoutitem.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtWidgets\qboxlayout.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtWidgets\QHBoxLayout \
		E:\Qt\6.6.3\msvc2019_64\include\QtWidgets\QLabel \
		E:\Qt\6.6.3\msvc2019_64\include\QtWidgets\qlabel.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtWidgets\qframe.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qpicture.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qtextdocument.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtWidgets\QLineEdit \
		E:\Qt\6.6.3\msvc2019_64\include\QtWidgets\qlineedit.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qtextcursor.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qtextformat.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qpen.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtGui\qtextoption.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtWidgets\QPushButton \
		E:\Qt\6.6.3\msvc2019_64\include\QtWidgets\qpushbutton.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtWidgets\qabstractbutton.h \
		E:\Qt\6.6.3\msvc2019_64\include\QtWidgets\QSpacerItem \
		E:\Qt\6.6.3\msvc2019_64\include\QtWidgets\QVBoxLayout
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Forelease\widget.obj ..\..\widget.cpp

release\qrc_res.obj: release\qrc_res.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Forelease\qrc_res.obj release\qrc_res.cpp

release\moc_widget.obj: release\moc_widget.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Forelease\moc_widget.obj release\moc_widget.cpp

####### Install

install:  FORCE

uninstall:  FORCE

FORCE:

