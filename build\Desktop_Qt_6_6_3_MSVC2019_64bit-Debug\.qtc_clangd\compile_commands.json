[{"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44996", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Downloads\\weatherforecast (2)\\weatherforecast", "-IE:\\Qt\\6.6.3\\msvc2019_64\\include", "-IE:\\Qt\\6.6.3\\msvc2019_64\\include\\QtWidgets", "-IE:\\Qt\\6.6.3\\msvc2019_64\\include\\QtGui", "-IE:\\Qt\\6.6.3\\msvc2019_64\\include\\QtNetwork", "-IE:\\Qt\\6.6.3\\msvc2019_64\\include\\QtCore", "-IC:\\Users\\<USER>\\Downloads\\weatherforecast (2)\\weatherforecast\\build\\Desktop_Qt_6_6_3_MSVC2019_64bit-Debug\\debug", "-IC:\\Users\\<USER>\\Downloads\\weatherforecast (2)\\weatherforecast\\build\\Desktop_Qt_6_6_3_MSVC2019_64bit-Debug", "-IE:\\Qt\\6.6.3\\msvc2019_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "C:\\Users\\<USER>\\Downloads\\weatherforecast (2)\\weatherforecast\\citycodeutils.cpp"], "directory": "C:/Users/<USER>/Downloads/weatherforecast (2)/weatherforecast/build/Desktop_Qt_6_6_3_MSVC2019_64bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Downloads/weatherforecast (2)/weatherforecast/citycodeutils.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44996", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Downloads\\weatherforecast (2)\\weatherforecast", "-IE:\\Qt\\6.6.3\\msvc2019_64\\include", "-IE:\\Qt\\6.6.3\\msvc2019_64\\include\\QtWidgets", "-IE:\\Qt\\6.6.3\\msvc2019_64\\include\\QtGui", "-IE:\\Qt\\6.6.3\\msvc2019_64\\include\\QtNetwork", "-IE:\\Qt\\6.6.3\\msvc2019_64\\include\\QtCore", "-IC:\\Users\\<USER>\\Downloads\\weatherforecast (2)\\weatherforecast\\build\\Desktop_Qt_6_6_3_MSVC2019_64bit-Debug\\debug", "-IC:\\Users\\<USER>\\Downloads\\weatherforecast (2)\\weatherforecast\\build\\Desktop_Qt_6_6_3_MSVC2019_64bit-Debug", "-IE:\\Qt\\6.6.3\\msvc2019_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "C:\\Users\\<USER>\\Downloads\\weatherforecast (2)\\weatherforecast\\main.cpp"], "directory": "C:/Users/<USER>/Downloads/weatherforecast (2)/weatherforecast/build/Desktop_Qt_6_6_3_MSVC2019_64bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Downloads/weatherforecast (2)/weatherforecast/main.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44996", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Downloads\\weatherforecast (2)\\weatherforecast", "-IE:\\Qt\\6.6.3\\msvc2019_64\\include", "-IE:\\Qt\\6.6.3\\msvc2019_64\\include\\QtWidgets", "-IE:\\Qt\\6.6.3\\msvc2019_64\\include\\QtGui", "-IE:\\Qt\\6.6.3\\msvc2019_64\\include\\QtNetwork", "-IE:\\Qt\\6.6.3\\msvc2019_64\\include\\QtCore", "-IC:\\Users\\<USER>\\Downloads\\weatherforecast (2)\\weatherforecast\\build\\Desktop_Qt_6_6_3_MSVC2019_64bit-Debug\\debug", "-IC:\\Users\\<USER>\\Downloads\\weatherforecast (2)\\weatherforecast\\build\\Desktop_Qt_6_6_3_MSVC2019_64bit-Debug", "-IE:\\Qt\\6.6.3\\msvc2019_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "C:\\Users\\<USER>\\Downloads\\weatherforecast (2)\\weatherforecast\\widget.cpp"], "directory": "C:/Users/<USER>/Downloads/weatherforecast (2)/weatherforecast/build/Desktop_Qt_6_6_3_MSVC2019_64bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Downloads/weatherforecast (2)/weatherforecast/widget.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44996", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Downloads\\weatherforecast (2)\\weatherforecast", "-IE:\\Qt\\6.6.3\\msvc2019_64\\include", "-IE:\\Qt\\6.6.3\\msvc2019_64\\include\\QtWidgets", "-IE:\\Qt\\6.6.3\\msvc2019_64\\include\\QtGui", "-IE:\\Qt\\6.6.3\\msvc2019_64\\include\\QtNetwork", "-IE:\\Qt\\6.6.3\\msvc2019_64\\include\\QtCore", "-IC:\\Users\\<USER>\\Downloads\\weatherforecast (2)\\weatherforecast\\build\\Desktop_Qt_6_6_3_MSVC2019_64bit-Debug\\debug", "-IC:\\Users\\<USER>\\Downloads\\weatherforecast (2)\\weatherforecast\\build\\Desktop_Qt_6_6_3_MSVC2019_64bit-Debug", "-IE:\\Qt\\6.6.3\\msvc2019_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "C:\\Users\\<USER>\\Downloads\\weatherforecast (2)\\weatherforecast\\citycodeutils.h"], "directory": "C:/Users/<USER>/Downloads/weatherforecast (2)/weatherforecast/build/Desktop_Qt_6_6_3_MSVC2019_64bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Downloads/weatherforecast (2)/weatherforecast/citycodeutils.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44996", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Downloads\\weatherforecast (2)\\weatherforecast", "-IE:\\Qt\\6.6.3\\msvc2019_64\\include", "-IE:\\Qt\\6.6.3\\msvc2019_64\\include\\QtWidgets", "-IE:\\Qt\\6.6.3\\msvc2019_64\\include\\QtGui", "-IE:\\Qt\\6.6.3\\msvc2019_64\\include\\QtNetwork", "-IE:\\Qt\\6.6.3\\msvc2019_64\\include\\QtCore", "-IC:\\Users\\<USER>\\Downloads\\weatherforecast (2)\\weatherforecast\\build\\Desktop_Qt_6_6_3_MSVC2019_64bit-Debug\\debug", "-IC:\\Users\\<USER>\\Downloads\\weatherforecast (2)\\weatherforecast\\build\\Desktop_Qt_6_6_3_MSVC2019_64bit-Debug", "-IE:\\Qt\\6.6.3\\msvc2019_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "C:\\Users\\<USER>\\Downloads\\weatherforecast (2)\\weatherforecast\\widget.h"], "directory": "C:/Users/<USER>/Downloads/weatherforecast (2)/weatherforecast/build/Desktop_Qt_6_6_3_MSVC2019_64bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Downloads/weatherforecast (2)/weatherforecast/widget.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44996", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Downloads\\weatherforecast (2)\\weatherforecast", "-IE:\\Qt\\6.6.3\\msvc2019_64\\include", "-IE:\\Qt\\6.6.3\\msvc2019_64\\include\\QtWidgets", "-IE:\\Qt\\6.6.3\\msvc2019_64\\include\\QtGui", "-IE:\\Qt\\6.6.3\\msvc2019_64\\include\\QtNetwork", "-IE:\\Qt\\6.6.3\\msvc2019_64\\include\\QtCore", "-IC:\\Users\\<USER>\\Downloads\\weatherforecast (2)\\weatherforecast\\build\\Desktop_Qt_6_6_3_MSVC2019_64bit-Debug\\debug", "-IC:\\Users\\<USER>\\Downloads\\weatherforecast (2)\\weatherforecast\\build\\Desktop_Qt_6_6_3_MSVC2019_64bit-Debug", "-IE:\\Qt\\6.6.3\\msvc2019_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "C:\\Users\\<USER>\\Downloads\\weatherforecast (2)\\weatherforecast\\build\\Desktop_Qt_6_6_3_MSVC2019_64bit-Debug\\ui_widget.h"], "directory": "C:/Users/<USER>/Downloads/weatherforecast (2)/weatherforecast/build/Desktop_Qt_6_6_3_MSVC2019_64bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Downloads/weatherforecast (2)/weatherforecast/build/Desktop_Qt_6_6_3_MSVC2019_64bit-Debug/ui_widget.h"}]