#include "widget.h"
#include "ui_widget.h"

Widget::Widget(QWidget *parent)
    : QWidget(parent)
    , ui(new Ui::Widget)
{
    ui->setupUi(this);
    setFixedSize(481,721);
    setWindowFlag(Qt::FramelessWindowHint);
    menuQuit = new QMenu(this);
    QAction *openAct =new QAction(QIcon(":/res/close.png"),tr("Quit"),this);
    menuQuit->addAction(openAct);
    connect(menuQuit,&QMenu::triggered,this,[this]{
        this->close();
    });

    manager =new QNetworkAccessManager(this);
    URL="http://t.weather.itboy.net/api/weather/city/101010100";
    QUrl urlTianqi(URL);
    QNetworkRequest res(urlTianqi);
    reply = manager->get(res);
    connect(manager,&QNetworkAccessManager::finished,this,&Widget::readHttpReply);
    TypeMap.insert("暴雪", ":/res/baoxue.png");
    TypeMap.insert("小雪", ":/res/xiaoxue.png");
    TypeMap.insert("中雪", ":/res/zhongxue.png");
    TypeMap.insert("大雨", ":/res/dayu.png");
    TypeMap.insert("小雨", ":/res/xiaoyu.png");
    TypeMap.insert("阵雪", ":/res/zhenyu.png");
    TypeMap.insert("雨夹雪", ":/res/yujiaxue.png");
    TypeMap.insert("中雨", ":/res/zhongyu.png");
    TypeMap.insert("大雨", ":/res/dayu.png");
    TypeMap.insert("暴雨", ":/res/baoyu.png");
    TypeMap.insert("雾", ":/res/wu.png");
    TypeMap.insert("沙尘暴", ":/res/shachenbao.png");
    TypeMap.insert("雷阵雨", ":/res/leizhenyu.png");
    TypeMap.insert("多云", ":/res/duoyun.png");
    TypeMap.insert("阴", ":/res/yin.png");
    TypeMap.insert("晴", ":/res/sun.png");
    TypeMap.insert("霾", ":/res/mai.png");
    TypeMap.insert("浮尘", ":/res/fuchen.png");
    TypeMap.insert("扬沙", ":/res/yangchen.png");
    TypeMap.insert("冻雨", ":/res/dongyu.png");
}

Widget::~Widget()
{
    delete ui;
}

void Widget::mousePressEvent(QMouseEvent *event)
{
    if(event->button()==Qt::RightButton){
        menuQuit->exec(QCursor::pos());
    }
    if(event->button()==Qt::LeftButton){
        mOffset=event->globalPos()-this->pos();
    }
}

void Widget::mouseMoveEvent(QMouseEvent *event)
{
    this->move(event->globalPos()-mOffset);
}
void Widget::parseWeatherJsonData(QByteArray rawData){
    QJsonDocument jsonObj =QJsonDocument::fromJson(rawData);
    if(!jsonObj.isNull()&& jsonObj.isObject()){
        QJsonObject objRoot =jsonObj.object();
        QString time =objRoot["time"].toString();
        QString date =objRoot["date"].toString();

        ui->labeldate->setText(time);
        QJsonObject cityInfo = jsonObj["cityInfo"].toObject();
        QString city =cityInfo["city"].toString();
        ui->labelcity->setText(city);
        QJsonObject data =objRoot["data"].toObject();
        QString wendu =data["wendu"].toString();
        ui->labelt->setText(wendu+"℃");
        QJsonArray forecastArray = data["forecast"].toArray();
        for (const QJsonValue &dayValue : forecastArray) {
            QJsonObject dayObj = dayValue.toObject();
            WeatherData dayData;
            dayData.date = dayObj["date"].toString();
            dayData.high = dayObj["high"].toString();
            dayData.low = dayObj["low"].toString();
            dayData.ymd = dayObj["ymd"].toString();
            dayData.week = dayObj["week"].toString();
            dayData.sunrise = dayObj["sunrise"].toString();
            dayData.sunset = dayObj["sunset"].toString();
            dayData.aqi = dayObj["aqi"].toInt();
            dayData.fx = dayObj["fx"].toString();
            dayData.fl = dayObj["fl"].toString();
            dayData.type = dayObj["type"].toString();
            dayData.notice = dayObj["notice"].toString();
            weatherForecast.append(dayData);
        }
        ui->labelrange->setText(weatherForecast[0].low+" "+weatherForecast[0].high);
        QString ganmao=data["ganmao"].toString();
        ui->label->setText("感冒指数："+ganmao);
        ui->labelFXtype->setText(weatherForecast[0].fx);
        ui->labelFXDATA->setText(weatherForecast[0].fl);
        QString shidu =data["shidu"].toString();
        ui->labelSDData->setText(shidu);
        int pm25Value = data["pm25"].toInt();
        QString pm25 = QString::number(pm25Value);
        ui->labelPM25Data->setText(pm25);
        QString KQ =data["quality"].toString();
        ui->labelKQData->setText(KQ);
        ui->labeltype->setText(weatherForecast[0].type);
        ui->labelWeathericon->setPixmap(TypeMap[weatherForecast[0].type]);
        //今天
        ui->labeldate2->setText(weatherForecast[0].ymd);
        ui->labelicon2->setPixmap(TypeMap[weatherForecast[0].type]);
        ui->labeliconData2->setText(weatherForecast[0].type);
        //昨天
        QJsonObject dataObj = objRoot["data"].toObject();
        QJsonObject yesterday = dataObj["yesterday"].toObject();
        QString ytype = yesterday["type"].toString();
        QString ymd = yesterday["ymd"].toString();
        ui->labeldate1->setText(ymd);
        ui->labelicon1->setPixmap(TypeMap[ytype]);
        ui->labeliconData1->setText(ytype);
        //明天labeldate3
        ui->labeldate3->setText(weatherForecast[1].ymd);
        ui->labelicon3->setPixmap(TypeMap[weatherForecast[1].type]);
        ui->labeliconData3->setText(weatherForecast[1].type);
        // 后天
        ui->labeldate4->setText(weatherForecast[2].ymd);
        ui->labelicon4->setPixmap(TypeMap[weatherForecast[2].type]);
        ui->labeliconData4->setText(weatherForecast[2].type);
        // 大后天
        ui->labeldate5->setText(weatherForecast[3].ymd);
        ui->labelicon5->setPixmap(TypeMap[weatherForecast[3].type]);
        ui->labeliconData5->setText(weatherForecast[3].type);
        // 大大后天
        ui->labeldate6->setText(weatherForecast[4].ymd);
        ui->labelicon6->setPixmap(TypeMap[weatherForecast[4].type]);
        ui->labeliconData6->setText(weatherForecast[4].type);
        weatherForecast.clear();
    }
}
void Widget::readHttpReply(QNetworkReply *reply)
{
    int resCode = reply->attribute(QNetworkRequest::HttpStatusCodeAttribute).toInt();
    if(reply->error()==QNetworkReply::NoError&& resCode==200){
        QByteArray data=reply->readAll();
        parseWeatherJsonData(data);
    }else{
        QMessageBox::warning(this,"错误","网络请求失败",QMessageBox::Ok);
    }
}
void Widget::on_pushButton_clicked()
{
    QString cityname =ui->lineEditcity->text();
    QString citycode =CityCodeUtils.getCityCode(cityname);
    if(!citycode.isEmpty()){
    URL = QString("http://t.weather.itboy.net/api/weather/city/%1")
        .arg(citycode);
        manager->get(QNetworkRequest(QUrl(URL)));
    }else{
        QMessageBox::warning(this,"错误","请输入正确的城市名称",QMessageBox::Ok);
    }
}

