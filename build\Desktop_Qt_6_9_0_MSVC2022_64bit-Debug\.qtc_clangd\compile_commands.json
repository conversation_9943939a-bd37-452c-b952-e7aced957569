[{"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.39", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\qt\\Tools\\Qt Creator 17.0.0-beta2\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\qt\\Tools\\Qt Creator 17.0.0-beta2\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IE:\\qtprogram\\weatherforecast", "-IE:\\qt\\6.9.0\\msvc2022_64\\include", "-IE:\\qt\\6.9.0\\msvc2022_64\\include\\QtWidgets", "-IE:\\qt\\6.9.0\\msvc2022_64\\include\\QtGui", "-IE:\\qt\\6.9.0\\msvc2022_64\\include\\QtCore", "-IE:\\qtprogram\\weatherforecast\\build\\Desktop_Qt_6_9_0_MSVC2022_64bit-Debug\\debug", "-IE:\\qtprogram\\weatherforecast\\build\\Desktop_Qt_6_9_0_MSVC2022_64bit-Debug", "-IE:\\qt\\6.9.0\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\qt\\Tools\\Qt Creator 17.0.0-beta2\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.39.33519\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.39.33519\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "E:\\qtprogram\\weatherforecast\\main.cpp"], "directory": "E:/qtprogram/weatherforecast/build/Desktop_Qt_6_9_0_MSVC2022_64bit-Debug/.qtc_clangd", "file": "E:/qtprogram/weatherforecast/main.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.39", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\qt\\Tools\\Qt Creator 17.0.0-beta2\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\qt\\Tools\\Qt Creator 17.0.0-beta2\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IE:\\qtprogram\\weatherforecast", "-IE:\\qt\\6.9.0\\msvc2022_64\\include", "-IE:\\qt\\6.9.0\\msvc2022_64\\include\\QtWidgets", "-IE:\\qt\\6.9.0\\msvc2022_64\\include\\QtGui", "-IE:\\qt\\6.9.0\\msvc2022_64\\include\\QtCore", "-IE:\\qtprogram\\weatherforecast\\build\\Desktop_Qt_6_9_0_MSVC2022_64bit-Debug\\debug", "-IE:\\qtprogram\\weatherforecast\\build\\Desktop_Qt_6_9_0_MSVC2022_64bit-Debug", "-IE:\\qt\\6.9.0\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\qt\\Tools\\Qt Creator 17.0.0-beta2\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.39.33519\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.39.33519\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "E:\\qtprogram\\weatherforecast\\widget.cpp"], "directory": "E:/qtprogram/weatherforecast/build/Desktop_Qt_6_9_0_MSVC2022_64bit-Debug/.qtc_clangd", "file": "E:/qtprogram/weatherforecast/widget.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.39", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\qt\\Tools\\Qt Creator 17.0.0-beta2\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\qt\\Tools\\Qt Creator 17.0.0-beta2\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IE:\\qtprogram\\weatherforecast", "-IE:\\qt\\6.9.0\\msvc2022_64\\include", "-IE:\\qt\\6.9.0\\msvc2022_64\\include\\QtWidgets", "-IE:\\qt\\6.9.0\\msvc2022_64\\include\\QtGui", "-IE:\\qt\\6.9.0\\msvc2022_64\\include\\QtCore", "-IE:\\qtprogram\\weatherforecast\\build\\Desktop_Qt_6_9_0_MSVC2022_64bit-Debug\\debug", "-IE:\\qtprogram\\weatherforecast\\build\\Desktop_Qt_6_9_0_MSVC2022_64bit-Debug", "-IE:\\qt\\6.9.0\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\qt\\Tools\\Qt Creator 17.0.0-beta2\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.39.33519\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.39.33519\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "E:\\qtprogram\\weatherforecast\\widget.h"], "directory": "E:/qtprogram/weatherforecast/build/Desktop_Qt_6_9_0_MSVC2022_64bit-Debug/.qtc_clangd", "file": "E:/qtprogram/weatherforecast/widget.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.39", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\qt\\Tools\\Qt Creator 17.0.0-beta2\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\qt\\Tools\\Qt Creator 17.0.0-beta2\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IE:\\qtprogram\\weatherforecast", "-IE:\\qt\\6.9.0\\msvc2022_64\\include", "-IE:\\qt\\6.9.0\\msvc2022_64\\include\\QtWidgets", "-IE:\\qt\\6.9.0\\msvc2022_64\\include\\QtGui", "-IE:\\qt\\6.9.0\\msvc2022_64\\include\\QtCore", "-IE:\\qtprogram\\weatherforecast\\build\\Desktop_Qt_6_9_0_MSVC2022_64bit-Debug\\debug", "-IE:\\qtprogram\\weatherforecast\\build\\Desktop_Qt_6_9_0_MSVC2022_64bit-Debug", "-IE:\\qt\\6.9.0\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\qt\\Tools\\Qt Creator 17.0.0-beta2\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.39.33519\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.39.33519\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "E:\\qtprogram\\weatherforecast\\build\\Desktop_Qt_6_9_0_MSVC2022_64bit-Debug\\ui_widget.h"], "directory": "E:/qtprogram/weatherforecast/build/Desktop_Qt_6_9_0_MSVC2022_64bit-Debug/.qtc_clangd", "file": "E:/qtprogram/weatherforecast/build/Desktop_Qt_6_9_0_MSVC2022_64bit-Debug/ui_widget.h"}]