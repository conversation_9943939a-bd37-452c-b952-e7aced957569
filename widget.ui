<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Widget</class>
 <widget class="QWidget" name="Widget">
  <property name="enabled">
   <bool>true</bool>
  </property>
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>492</width>
    <height>692</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>111</width>
    <height>31</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Widget</string>
  </property>
  <property name="styleSheet">
   <string notr="true">background-color: rgb(42, 34, 34);
color: rgb(250, 250, 250);</string>
  </property>
  <widget class="QWidget" name="layoutWidget">
   <property name="geometry">
    <rect>
     <x>10</x>
     <y>0</y>
     <width>466</width>
     <height>691</height>
    </rect>
   </property>
   <layout class="QVBoxLayout" name="verticalLayout_7" stretch="1,2,3,0,10">
    <item>
     <layout class="QHBoxLayout" name="horizontalLayout_6" stretch="0,0,0">
      <property name="spacing">
       <number>10</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <item>
       <widget class="QLineEdit" name="lineEditcity">
        <property name="styleSheet">
         <string notr="true">background-color: rgb(255, 255, 255);
border-radius:10px;
color: rgb(0, 0, 0);</string>
        </property>
        <property name="text">
         <string>请输入城市</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="pushButton">
        <property name="maximumSize">
         <size>
          <width>35</width>
          <height>35</height>
         </size>
        </property>
        <property name="styleSheet">
         <string notr="true">border-radius:10px;</string>
        </property>
        <property name="text">
         <string/>
        </property>
        <property name="icon">
         <iconset resource="res.qrc">
          <normaloff>:/res/search.png</normaloff>:/res/search.png</iconset>
        </property>
        <property name="checkable">
         <bool>false</bool>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QLabel" name="labeldate">
        <property name="minimumSize">
         <size>
          <width>30</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string>2023/12/10 星期日</string>
        </property>
       </widget>
      </item>
     </layout>
    </item>
    <item>
     <widget class="QWidget" name="widget02" native="true">
      <layout class="QHBoxLayout" name="horizontalLayout_5" stretch="0,0">
       <item>
        <widget class="QLabel" name="labelWeathericon">
         <property name="maximumSize">
          <size>
           <width>90</width>
           <height>90</height>
          </size>
         </property>
         <property name="text">
          <string/>
         </property>
         <property name="pixmap">
          <pixmap resource="res.qrc">:/res/sun.png</pixmap>
         </property>
         <property name="scaledContents">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       <item>
        <layout class="QGridLayout" name="gridLayout_2">
         <item row="0" column="0">
          <widget class="QLabel" name="labelt">
           <property name="font">
            <font>
             <pointsize>21</pointsize>
            </font>
           </property>
           <property name="text">
            <string>23</string>
           </property>
          </widget>
         </item>
         <item row="0" column="2">
          <widget class="QLabel" name="labelcity">
           <property name="minimumSize">
            <size>
             <width>52</width>
             <height>45</height>
            </size>
           </property>
           <property name="text">
            <string>深圳市</string>
           </property>
          </widget>
         </item>
         <item row="1" column="0">
          <widget class="QLabel" name="labeltype">
           <property name="text">
            <string>晴天</string>
           </property>
          </widget>
         </item>
         <item row="1" column="1">
          <widget class="QLabel" name="labelrange">
           <property name="minimumSize">
            <size>
             <width>252</width>
             <height>42</height>
            </size>
           </property>
           <property name="text">
            <string>20~26℃</string>
           </property>
          </widget>
         </item>
         <item row="1" column="2">
          <spacer name="horizontalSpacer">
           <property name="orientation">
            <enum>Qt::Orientation::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item row="0" column="1">
          <spacer name="horizontalSpacer_2">
           <property name="orientation">
            <enum>Qt::Orientation::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>250</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </item>
      </layout>
     </widget>
    </item>
    <item>
     <widget class="QWidget" name="widgetganmao" native="true">
      <property name="layoutDirection">
       <enum>Qt::LayoutDirection::LeftToRight</enum>
      </property>
      <layout class="QVBoxLayout" name="verticalLayout_2" stretch="0,0">
       <item>
        <widget class="QLabel" name="label">
         <property name="text">
          <string>感冒指数：各类人群可自由活动</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QWidget" name="widget0301" native="true">
         <property name="styleSheet">
          <string notr="true">background-color: rgb(255, 158, 89);
border-radius:20px</string>
         </property>
         <layout class="QGridLayout" name="gridLayout">
          <item row="0" column="0">
           <layout class="QHBoxLayout" name="horizontalLayout" stretch="0,0">
            <item>
             <widget class="QLabel" name="labelFX">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                <horstretch>45</horstretch>
                <verstretch>45</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>45</width>
                <height>45</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>45</width>
                <height>45</height>
               </size>
              </property>
              <property name="text">
               <string/>
              </property>
              <property name="pixmap">
               <pixmap resource="res.qrc">:/res/1.png</pixmap>
              </property>
              <property name="scaledContents">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item>
             <layout class="QVBoxLayout" name="verticalLayout" stretch="0,0">
              <item>
               <widget class="QLabel" name="labelFXtype">
                <property name="text">
                 <string>东南风</string>
                </property>
                <property name="alignment">
                 <set>Qt::AlignmentFlag::AlignCenter</set>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLabel" name="labelFXDATA">
                <property name="text">
                 <string>二级别</string>
                </property>
                <property name="alignment">
                 <set>Qt::AlignmentFlag::AlignCenter</set>
                </property>
               </widget>
              </item>
             </layout>
            </item>
           </layout>
          </item>
          <item row="0" column="1">
           <layout class="QHBoxLayout" name="horizontalLayout_2" stretch="0,0">
            <item>
             <widget class="QLabel" name="labelFX_2">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                <horstretch>45</horstretch>
                <verstretch>45</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>45</width>
                <height>45</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>45</width>
                <height>45</height>
               </size>
              </property>
              <property name="text">
               <string/>
              </property>
              <property name="pixmap">
               <pixmap resource="res.qrc">:/res/3.png</pixmap>
              </property>
              <property name="scaledContents">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item>
             <layout class="QVBoxLayout" name="verticalLayout_3" stretch="0,0">
              <item>
               <widget class="QLabel" name="labelPMtype">
                <property name="text">
                 <string>PM2.5</string>
                </property>
                <property name="alignment">
                 <set>Qt::AlignmentFlag::AlignCenter</set>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLabel" name="labelPM25Data">
                <property name="minimumSize">
                 <size>
                  <width>156</width>
                  <height>21</height>
                 </size>
                </property>
                <property name="text">
                 <string>24</string>
                </property>
                <property name="alignment">
                 <set>Qt::AlignmentFlag::AlignCenter</set>
                </property>
               </widget>
              </item>
             </layout>
            </item>
           </layout>
          </item>
          <item row="1" column="0">
           <layout class="QHBoxLayout" name="horizontalLayout_3" stretch="0,0">
            <item>
             <widget class="QLabel" name="labelFX_3">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                <horstretch>45</horstretch>
                <verstretch>45</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>45</width>
                <height>45</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>45</width>
                <height>45</height>
               </size>
              </property>
              <property name="text">
               <string/>
              </property>
              <property name="pixmap">
               <pixmap resource="res.qrc">:/res/4.png</pixmap>
              </property>
              <property name="scaledContents">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item>
             <layout class="QVBoxLayout" name="verticalLayout_4" stretch="0,0">
              <item>
               <widget class="QLabel" name="labelSDtype">
                <property name="text">
                 <string>湿度</string>
                </property>
                <property name="alignment">
                 <set>Qt::AlignmentFlag::AlignCenter</set>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLabel" name="labelSDData">
                <property name="text">
                 <string>85%</string>
                </property>
                <property name="alignment">
                 <set>Qt::AlignmentFlag::AlignCenter</set>
                </property>
               </widget>
              </item>
             </layout>
            </item>
           </layout>
          </item>
          <item row="1" column="1">
           <layout class="QHBoxLayout" name="horizontalLayout_4" stretch="0,0">
            <item>
             <widget class="QLabel" name="labelFX_4">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                <horstretch>45</horstretch>
                <verstretch>45</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>45</width>
                <height>45</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>45</width>
                <height>45</height>
               </size>
              </property>
              <property name="text">
               <string/>
              </property>
              <property name="pixmap">
               <pixmap resource="res.qrc">:/res/1.png</pixmap>
              </property>
              <property name="scaledContents">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item>
             <layout class="QVBoxLayout" name="verticalLayout_5" stretch="0,0">
              <item>
               <widget class="QLabel" name="labelKQtype">
                <property name="text">
                 <string>空气质量</string>
                </property>
                <property name="alignment">
                 <set>Qt::AlignmentFlag::AlignCenter</set>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLabel" name="labelKQData">
                <property name="text">
                 <string>2级</string>
                </property>
                <property name="alignment">
                 <set>Qt::AlignmentFlag::AlignCenter</set>
                </property>
               </widget>
              </item>
             </layout>
            </item>
           </layout>
          </item>
         </layout>
        </widget>
       </item>
      </layout>
     </widget>
    </item>
    <item>
     <widget class="QWidget" name="Widget3" native="true">
      <property name="minimumSize">
       <size>
        <width>0</width>
        <height>50</height>
       </size>
      </property>
      <layout class="QGridLayout" name="gridLayout_3">
       <property name="spacing">
        <number>0</number>
       </property>
       <item row="0" column="0">
        <widget class="QLabel" name="labelday1">
         <property name="styleSheet">
          <string notr="true">background-color: rgb(67, 200, 200);
border-radius:7px;
border-bottom-right-radius:0px;
border-bottom-left-radius:0px;</string>
         </property>
         <property name="text">
          <string>昨天</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignmentFlag::AlignCenter</set>
         </property>
        </widget>
       </item>
       <item row="0" column="1">
        <widget class="QLabel" name="labelday2">
         <property name="styleSheet">
          <string notr="true">background-color: rgb(67, 200, 200);
border-radius:7px;
border-bottom-right-radius:0px;
border-bottom-left-radius:0px;</string>
         </property>
         <property name="text">
          <string>今天</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignmentFlag::AlignCenter</set>
         </property>
        </widget>
       </item>
       <item row="0" column="2">
        <widget class="QLabel" name="labelday3">
         <property name="styleSheet">
          <string notr="true">background-color: rgb(67, 200, 200);
border-radius:7px;
border-bottom-right-radius:0px;
border-bottom-left-radius:0px;</string>
         </property>
         <property name="text">
          <string>明天</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignmentFlag::AlignCenter</set>
         </property>
        </widget>
       </item>
       <item row="0" column="3">
        <widget class="QLabel" name="labelday4">
         <property name="styleSheet">
          <string notr="true">background-color: rgb(67, 200, 200);
border-radius:7px;
border-bottom-right-radius:0px;
border-bottom-left-radius:0px;</string>
         </property>
         <property name="text">
          <string>星期二</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignmentFlag::AlignCenter</set>
         </property>
        </widget>
       </item>
       <item row="0" column="4">
        <widget class="QLabel" name="labelday5">
         <property name="styleSheet">
          <string notr="true">background-color: rgb(67, 200, 200);
border-radius:7px;
border-bottom-right-radius:0px;
border-bottom-left-radius:0px;</string>
         </property>
         <property name="text">
          <string>星期三</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignmentFlag::AlignCenter</set>
         </property>
        </widget>
       </item>
       <item row="0" column="5">
        <widget class="QLabel" name="labelday6">
         <property name="styleSheet">
          <string notr="true">background-color: rgb(67, 200, 200);
border-radius:7px;
border-bottom-right-radius:0px;
border-bottom-left-radius:0px;</string>
         </property>
         <property name="text">
          <string>星期四</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignmentFlag::AlignCenter</set>
         </property>
        </widget>
       </item>
       <item row="1" column="0">
        <widget class="QLabel" name="labeldate1">
         <property name="minimumSize">
          <size>
           <width>74</width>
           <height>20</height>
          </size>
         </property>
         <property name="styleSheet">
          <string notr="true">background-color: rgb(67, 200, 200);
border-radius:7px;
border-top-right-radius:0px;
border-top-left-radius:0px;</string>
         </property>
         <property name="text">
          <string>12/9</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignmentFlag::AlignCenter</set>
         </property>
        </widget>
       </item>
       <item row="1" column="1">
        <widget class="QLabel" name="labeldate2">
         <property name="styleSheet">
          <string notr="true">background-color: rgb(67, 200, 200);
border-radius:7px;
border-top-right-radius:0px;
border-top-left-radius:0px;</string>
         </property>
         <property name="text">
          <string>12/9</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignmentFlag::AlignCenter</set>
         </property>
        </widget>
       </item>
       <item row="1" column="2">
        <widget class="QLabel" name="labeldate3">
         <property name="styleSheet">
          <string notr="true">background-color: rgb(67, 200, 200);
border-radius:7px;
border-top-right-radius:0px;
border-top-left-radius:0px;</string>
         </property>
         <property name="text">
          <string>12/9</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignmentFlag::AlignCenter</set>
         </property>
        </widget>
       </item>
       <item row="1" column="3">
        <widget class="QLabel" name="labeldate4">
         <property name="styleSheet">
          <string notr="true">background-color: rgb(67, 200, 200);
border-radius:7px;
border-top-right-radius:0px;
border-top-left-radius:0px;</string>
         </property>
         <property name="text">
          <string>12/9</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignmentFlag::AlignCenter</set>
         </property>
        </widget>
       </item>
       <item row="1" column="4">
        <widget class="QLabel" name="labeldate5">
         <property name="styleSheet">
          <string notr="true">background-color: rgb(67, 200, 200);
border-radius:7px;
border-top-right-radius:0px;
border-top-left-radius:0px;</string>
         </property>
         <property name="text">
          <string>12/9</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignmentFlag::AlignCenter</set>
         </property>
        </widget>
       </item>
       <item row="1" column="5">
        <widget class="QLabel" name="labeldate6">
         <property name="styleSheet">
          <string notr="true">background-color: rgb(67, 200, 200);
border-radius:7px;
border-top-right-radius:0px;
border-top-left-radius:0px;</string>
         </property>
         <property name="text">
          <string>12/9</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignmentFlag::AlignCenter</set>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
    </item>
    <item>
     <widget class="QWidget" name="Widget04" native="true">
      <layout class="QVBoxLayout" name="verticalLayout_6" stretch="0,0,0,0,0">
       <item>
        <widget class="QWidget" name="widget_3" native="true">
         <layout class="QGridLayout" name="gridLayout_4">
          <property name="horizontalSpacing">
           <number>25</number>
          </property>
          <property name="verticalSpacing">
           <number>0</number>
          </property>
          <item row="1" column="4">
           <widget class="QLabel" name="labeliconData5">
            <property name="minimumSize">
             <size>
              <width>46</width>
              <height>20</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">background-color: rgb(83, 83, 83);
border-radius:7px;
border-top-right-radius:0px;
border-top-left-radius:0px;</string>
            </property>
            <property name="text">
             <string>多云</string>
            </property>
            <property name="scaledContents">
             <bool>false</bool>
            </property>
            <property name="alignment">
             <set>Qt::AlignmentFlag::AlignCenter</set>
            </property>
           </widget>
          </item>
          <item row="0" column="3">
           <widget class="QLabel" name="labelicon4">
            <property name="minimumSize">
             <size>
              <width>46</width>
              <height>32</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>41</width>
              <height>31</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">background-color: rgb(83, 83, 83);
border-radius:7px;
border-bottom-right-radius:0px;
border-bottom-left-radius:0px;</string>
            </property>
            <property name="text">
             <string/>
            </property>
            <property name="pixmap">
             <pixmap resource="res.qrc">:/res/duoyun.png</pixmap>
            </property>
            <property name="scaledContents">
             <bool>true</bool>
            </property>
           </widget>
          </item>
          <item row="1" column="3">
           <widget class="QLabel" name="labeliconData4">
            <property name="minimumSize">
             <size>
              <width>46</width>
              <height>20</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">background-color: rgb(83, 83, 83);
border-radius:7px;
border-top-right-radius:0px;
border-top-left-radius:0px;</string>
            </property>
            <property name="text">
             <string>多云</string>
            </property>
            <property name="scaledContents">
             <bool>false</bool>
            </property>
            <property name="alignment">
             <set>Qt::AlignmentFlag::AlignCenter</set>
            </property>
           </widget>
          </item>
          <item row="0" column="0">
           <widget class="QLabel" name="labelicon1">
            <property name="minimumSize">
             <size>
              <width>46</width>
              <height>32</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>41</width>
              <height>31</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">background-color: rgb(83, 83, 83);
border-radius:7px;
border-bottom-right-radius:0px;
border-bottom-left-radius:0px;</string>
            </property>
            <property name="text">
             <string/>
            </property>
            <property name="pixmap">
             <pixmap resource="res.qrc">:/res/duoyun.png</pixmap>
            </property>
            <property name="scaledContents">
             <bool>true</bool>
            </property>
           </widget>
          </item>
          <item row="1" column="5">
           <widget class="QLabel" name="labeliconData6">
            <property name="minimumSize">
             <size>
              <width>46</width>
              <height>20</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">background-color: rgb(83, 83, 83);
border-radius:7px;
border-top-right-radius:0px;
border-top-left-radius:0px;</string>
            </property>
            <property name="text">
             <string>多云</string>
            </property>
            <property name="scaledContents">
             <bool>false</bool>
            </property>
            <property name="alignment">
             <set>Qt::AlignmentFlag::AlignCenter</set>
            </property>
           </widget>
          </item>
          <item row="1" column="2">
           <widget class="QLabel" name="labeliconData3">
            <property name="minimumSize">
             <size>
              <width>46</width>
              <height>20</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">background-color: rgb(83, 83, 83);
border-radius:7px;
border-top-right-radius:0px;
border-top-left-radius:0px;</string>
            </property>
            <property name="text">
             <string>多云</string>
            </property>
            <property name="scaledContents">
             <bool>false</bool>
            </property>
            <property name="alignment">
             <set>Qt::AlignmentFlag::AlignCenter</set>
            </property>
           </widget>
          </item>
          <item row="0" column="1">
           <widget class="QLabel" name="labelicon2">
            <property name="minimumSize">
             <size>
              <width>46</width>
              <height>32</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>41</width>
              <height>31</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">background-color: rgb(83, 83, 83);
border-radius:7px;
border-bottom-right-radius:0px;
border-bottom-left-radius:0px;</string>
            </property>
            <property name="text">
             <string/>
            </property>
            <property name="pixmap">
             <pixmap resource="res.qrc">:/res/duoyun.png</pixmap>
            </property>
            <property name="scaledContents">
             <bool>true</bool>
            </property>
           </widget>
          </item>
          <item row="0" column="2">
           <widget class="QLabel" name="labelicon3">
            <property name="minimumSize">
             <size>
              <width>46</width>
              <height>32</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>41</width>
              <height>31</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">background-color: rgb(83, 83, 83);
border-radius:7px;
border-bottom-right-radius:0px;
border-bottom-left-radius:0px;</string>
            </property>
            <property name="text">
             <string/>
            </property>
            <property name="pixmap">
             <pixmap resource="res.qrc">:/res/duoyun.png</pixmap>
            </property>
            <property name="scaledContents">
             <bool>true</bool>
            </property>
           </widget>
          </item>
          <item row="1" column="1">
           <widget class="QLabel" name="labeliconData2">
            <property name="minimumSize">
             <size>
              <width>46</width>
              <height>20</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">background-color: rgb(83, 83, 83);
border-radius:7px;
border-top-right-radius:0px;
border-top-left-radius:0px;</string>
            </property>
            <property name="text">
             <string>多云</string>
            </property>
            <property name="scaledContents">
             <bool>false</bool>
            </property>
            <property name="alignment">
             <set>Qt::AlignmentFlag::AlignCenter</set>
            </property>
           </widget>
          </item>
          <item row="0" column="5">
           <widget class="QLabel" name="labelicon6">
            <property name="minimumSize">
             <size>
              <width>46</width>
              <height>32</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>41</width>
              <height>31</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">background-color: rgb(83, 83, 83);
border-radius:7px;
border-bottom-right-radius:0px;
border-bottom-left-radius:0px;</string>
            </property>
            <property name="text">
             <string/>
            </property>
            <property name="pixmap">
             <pixmap resource="res.qrc">:/res/duoyun.png</pixmap>
            </property>
            <property name="scaledContents">
             <bool>true</bool>
            </property>
           </widget>
          </item>
          <item row="1" column="0">
           <widget class="QLabel" name="labeliconData1">
            <property name="minimumSize">
             <size>
              <width>46</width>
              <height>20</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">background-color: rgb(83, 83, 83);
border-radius:7px;
border-top-right-radius:0px;
border-top-left-radius:0px;</string>
            </property>
            <property name="text">
             <string>多云</string>
            </property>
            <property name="scaledContents">
             <bool>false</bool>
            </property>
            <property name="alignment">
             <set>Qt::AlignmentFlag::AlignCenter</set>
            </property>
           </widget>
          </item>
          <item row="0" column="4">
           <widget class="QLabel" name="labelicon5">
            <property name="minimumSize">
             <size>
              <width>46</width>
              <height>32</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>41</width>
              <height>31</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">background-color: rgb(83, 83, 83);
border-radius:7px;
border-bottom-right-radius:0px;
border-bottom-left-radius:0px;</string>
            </property>
            <property name="text">
             <string/>
            </property>
            <property name="pixmap">
             <pixmap resource="res.qrc">:/res/duoyun.png</pixmap>
            </property>
            <property name="scaledContents">
             <bool>true</bool>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_7" stretch="0,0,0,0,0,0">
         <item>
          <widget class="QLabel" name="labelair1">
           <property name="styleSheet">
            <string notr="true">background-color: rgb(88, 177, 83);
border-radius:7px;</string>
           </property>
           <property name="text">
            <string>优</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignmentFlag::AlignCenter</set>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="labelair2">
           <property name="styleSheet">
            <string notr="true">background-color: rgb(209, 195, 39);
border-radius:7px;</string>
           </property>
           <property name="text">
            <string>良</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignmentFlag::AlignCenter</set>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="labelair3">
           <property name="styleSheet">
            <string notr="true">background-color: rgb(88, 177, 83);
border-radius:7px;</string>
           </property>
           <property name="text">
            <string>优</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignmentFlag::AlignCenter</set>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="labelair4">
           <property name="styleSheet">
            <string notr="true">background-color: rgb(88, 177, 83);
border-radius:7px;</string>
           </property>
           <property name="text">
            <string>优</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignmentFlag::AlignCenter</set>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="labelair5">
           <property name="styleSheet">
            <string notr="true">background-color: rgb(88, 177, 83);
border-radius:7px;</string>
           </property>
           <property name="text">
            <string>优</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignmentFlag::AlignCenter</set>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="labelair6">
           <property name="styleSheet">
            <string notr="true">background-color: rgb(88, 177, 83);
border-radius:7px;</string>
           </property>
           <property name="text">
            <string>优</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignmentFlag::AlignCenter</set>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <widget class="QWidget" name="widget" native="true">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>70</height>
          </size>
         </property>
         <property name="styleSheet">
          <string notr="true">background-color: rgb(152, 152, 152);
border-radius:10px;</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QWidget" name="widget_2" native="true">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>70</height>
          </size>
         </property>
         <property name="styleSheet">
          <string notr="true">background-color: rgb(152, 152, 152);
border-radius:10px;</string>
         </property>
        </widget>
       </item>
       <item>
        <layout class="QGridLayout" name="gridLayout_6">
         <property name="verticalSpacing">
          <number>0</number>
         </property>
         <item row="0" column="0">
          <widget class="QLabel" name="labelFX1">
           <property name="styleSheet">
            <string notr="true">background-color: rgb(135, 135, 135);
border-radius:7px;
border-bottom-right-radius:0px;
border-bottom-left-radius:0px;</string>
           </property>
           <property name="text">
            <string>东南风</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignmentFlag::AlignCenter</set>
           </property>
          </widget>
         </item>
         <item row="0" column="1">
          <widget class="QLabel" name="labelFX2">
           <property name="styleSheet">
            <string notr="true">background-color: rgb(135, 135, 135);
border-radius:7px;
border-bottom-right-radius:0px;
border-bottom-left-radius:0px;</string>
           </property>
           <property name="text">
            <string>东南风</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignmentFlag::AlignCenter</set>
           </property>
          </widget>
         </item>
         <item row="0" column="2">
          <widget class="QLabel" name="labelFX3">
           <property name="styleSheet">
            <string notr="true">background-color: rgb(135, 135, 135);
border-radius:7px;
border-bottom-right-radius:0px;
border-bottom-left-radius:0px;</string>
           </property>
           <property name="text">
            <string>东南风</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignmentFlag::AlignCenter</set>
           </property>
          </widget>
         </item>
         <item row="0" column="3">
          <widget class="QLabel" name="labelFX4">
           <property name="styleSheet">
            <string notr="true">background-color: rgb(135, 135, 135);
border-radius:7px;
border-bottom-right-radius:0px;
border-bottom-left-radius:0px;</string>
           </property>
           <property name="text">
            <string>东南风</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignmentFlag::AlignCenter</set>
           </property>
          </widget>
         </item>
         <item row="0" column="4">
          <widget class="QLabel" name="labelFX5">
           <property name="styleSheet">
            <string notr="true">background-color: rgb(135, 135, 135);
border-radius:7px;
border-bottom-right-radius:0px;
border-bottom-left-radius:0px;</string>
           </property>
           <property name="text">
            <string>东南风</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignmentFlag::AlignCenter</set>
           </property>
          </widget>
         </item>
         <item row="0" column="5">
          <widget class="QLabel" name="labelFX6">
           <property name="styleSheet">
            <string notr="true">background-color: rgb(135, 135, 135);
border-radius:7px;
border-bottom-right-radius:0px;
border-bottom-left-radius:0px;</string>
           </property>
           <property name="text">
            <string>东南风</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignmentFlag::AlignCenter</set>
           </property>
          </widget>
         </item>
         <item row="1" column="0">
          <widget class="QLabel" name="labelFXData1">
           <property name="styleSheet">
            <string notr="true">background-color: rgb(135, 135, 135);
border-radius:7px;
border-top-right-radius:0px;
border-top-left-radius:0px;</string>
           </property>
           <property name="text">
            <string>二级</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignmentFlag::AlignCenter</set>
           </property>
          </widget>
         </item>
         <item row="1" column="1">
          <widget class="QLabel" name="labelFXData2">
           <property name="styleSheet">
            <string notr="true">background-color: rgb(135, 135, 135);
border-radius:7px;
border-top-right-radius:0px;
border-top-left-radius:0px;</string>
           </property>
           <property name="text">
            <string>二级</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignmentFlag::AlignCenter</set>
           </property>
          </widget>
         </item>
         <item row="1" column="2">
          <widget class="QLabel" name="labelFXData3">
           <property name="styleSheet">
            <string notr="true">background-color: rgb(135, 135, 135);
border-radius:7px;
border-top-right-radius:0px;
border-top-left-radius:0px;</string>
           </property>
           <property name="text">
            <string>二级</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignmentFlag::AlignCenter</set>
           </property>
          </widget>
         </item>
         <item row="1" column="3">
          <widget class="QLabel" name="labelFXData4">
           <property name="styleSheet">
            <string notr="true">background-color: rgb(135, 135, 135);
border-radius:7px;
border-top-right-radius:0px;
border-top-left-radius:0px;</string>
           </property>
           <property name="text">
            <string>二级</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignmentFlag::AlignCenter</set>
           </property>
          </widget>
         </item>
         <item row="1" column="4">
          <widget class="QLabel" name="labelFXData5">
           <property name="styleSheet">
            <string notr="true">background-color: rgb(135, 135, 135);
border-radius:7px;
border-top-right-radius:0px;
border-top-left-radius:0px;</string>
           </property>
           <property name="text">
            <string>二级</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignmentFlag::AlignCenter</set>
           </property>
          </widget>
         </item>
         <item row="1" column="5">
          <widget class="QLabel" name="labelFXData6">
           <property name="styleSheet">
            <string notr="true">background-color: rgb(135, 135, 135);
border-radius:7px;
border-top-right-radius:0px;
border-top-left-radius:0px;</string>
           </property>
           <property name="text">
            <string>二级</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignmentFlag::AlignCenter</set>
           </property>
          </widget>
         </item>
        </layout>
       </item>
      </layout>
     </widget>
    </item>
   </layout>
  </widget>
 </widget>
 <resources>
  <include location="res.qrc"/>
 </resources>
 <connections/>
</ui>
