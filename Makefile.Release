#############################################################################
# Makefile for building: weatherforecast
# Generated by qmake (3.1) (Qt 6.9.0)
# Project:  weatherforecast.pro
# Template: app
#############################################################################

MAKEFILE      = Makefile.Release

EQ            = =

####### Compiler, tools and options

CC            = cl
CXX           = cl
DEFINES       = -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DNDEBUG -DQT_QML_DEBUG -DQT_NO_DEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB
CFLAGS        = -nologo -Zc:wchar_t -FS -Zc:strictStrings -O2 -MD -utf-8 -W3 -w44456 -w44457 -w44458 $(DEFINES)
CXXFLAGS      = -nologo -Zc:wchar_t -FS -Zc:rvalueCast -Zc:inline -Zc:strictStrings -Zc:throwingNew -permissive- -Zc:__cplusplus -Zc:externConstexpr -O2 -MD -std:c++17 -utf-8 -W3 -w34100 -w34189 -w44456 -w44457 -w44458 -wd4577 -wd4467 -EHsc $(DEFINES)
INCPATH       = -I. -I..\..\qt\6.9.0\msvc2022_64\include -I..\..\qt\6.9.0\msvc2022_64\include\QtWidgets -I..\..\qt\6.9.0\msvc2022_64\include\QtGui -I..\..\qt\6.9.0\msvc2022_64\include\QtNetwork -I..\..\qt\6.9.0\msvc2022_64\include\QtCore -Irelease -I. -I/include -I..\..\qt\6.9.0\msvc2022_64\mkspecs\win32-msvc 
LINKER        = link
LFLAGS        = /NOLOGO /DYNAMICBASE /NXCOMPAT /OPT:REF /OPT:ICF /INCREMENTAL:NO /SUBSYSTEM:WINDOWS "/MANIFESTDEPENDENCY:type='win32' name='Microsoft.Windows.Common-Controls' version='6.0.0.0' publicKeyToken='6595b64144ccf1df' language='*' processorArchitecture='*'"
LIBS          = E:\qt\6.9.0\msvc2022_64\lib\Qt6Widgets.lib E:\qt\6.9.0\msvc2022_64\lib\Qt6Gui.lib E:\qt\6.9.0\msvc2022_64\lib\Qt6Network.lib E:\qt\6.9.0\msvc2022_64\lib\Qt6Core.lib E:\qt\6.9.0\msvc2022_64\lib\Qt6EntryPoint.lib shell32.lib  
QMAKE         = E:\qt\6.9.0\msvc2022_64\bin\qmake.exe
DEL_FILE      = del
CHK_DIR_EXISTS= if not exist
MKDIR         = mkdir
COPY          = copy /y
COPY_FILE     = copy /y
COPY_DIR      = xcopy /s /q /y /i
INSTALL_FILE  = copy /y
INSTALL_PROGRAM = copy /y
INSTALL_DIR   = xcopy /s /q /y /i
QINSTALL      = E:\qt\6.9.0\msvc2022_64\bin\qmake.exe -install qinstall
QINSTALL_PROGRAM = E:\qt\6.9.0\msvc2022_64\bin\qmake.exe -install qinstall -exe
DEL_FILE      = del
SYMLINK       = $(QMAKE) -install ln -f -s
DEL_DIR       = rmdir
MOVE          = move
IDC           = idc
IDL           = midl
ZIP           = zip -r -9
DEF_FILE      = 
RES_FILE      = 
SED           = $(QMAKE) -install sed
MOVE          = move

####### Output directory

OBJECTS_DIR   = release

####### Files

SOURCES       = citycodeutils.cpp \
		main.cpp \
		widget.cpp release\qrc_res.cpp \
		release\moc_widget.cpp
OBJECTS       = release\citycodeutils.obj \
		release\main.obj \
		release\widget.obj \
		release\qrc_res.obj \
		release\moc_widget.obj

DIST          =  citycodeutils.h \
		widget.h citycodeutils.cpp \
		main.cpp \
		widget.cpp
QMAKE_TARGET  = weatherforecast
DESTDIR        = release\ #avoid trailing-slash linebreak
TARGET         = weatherforecast.exe
DESTDIR_TARGET = release\weatherforecast.exe

####### Implicit rules

.SUFFIXES: .c .cpp .cc .cxx

{.}.cpp{release\}.obj::
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Forelease\ @<<
	$<
<<

{.}.cc{release\}.obj::
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Forelease\ @<<
	$<
<<

{.}.cxx{release\}.obj::
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Forelease\ @<<
	$<
<<

{.}.c{release\}.obj::
	$(CC) -c $(CFLAGS) $(INCPATH) -Forelease\ @<<
	$<
<<

{release}.cpp{release\}.obj::
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Forelease\ @<<
	$<
<<

{release}.cc{release\}.obj::
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Forelease\ @<<
	$<
<<

{release}.cxx{release\}.obj::
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Forelease\ @<<
	$<
<<

{release}.c{release\}.obj::
	$(CC) -c $(CFLAGS) $(INCPATH) -Forelease\ @<<
	$<
<<

####### Build rules

first: all
all: Makefile.Release  release\weatherforecast.exe

release\weatherforecast.exe: E:\qt\6.9.0\msvc2022_64\lib\Qt6Widgets.lib E:\qt\6.9.0\msvc2022_64\lib\Qt6Gui.lib E:\qt\6.9.0\msvc2022_64\lib\Qt6Network.lib E:\qt\6.9.0\msvc2022_64\lib\Qt6Core.lib E:\qt\6.9.0\msvc2022_64\lib\Qt6EntryPoint.lib ui_widget.h $(OBJECTS) 
	$(LINKER) $(LFLAGS) /MANIFEST:embed /OUT:$(DESTDIR_TARGET) @<<
release\citycodeutils.obj release\main.obj release\widget.obj release\qrc_res.obj release\moc_widget.obj
$(LIBS)
<<

qmake: FORCE
	@$(QMAKE) -o Makefile.Release weatherforecast.pro -spec win32-msvc "CONFIG+=debug" "CONFIG+=qml_debug"

qmake_all: FORCE

dist:
	$(ZIP) weatherforecast.zip $(SOURCES) $(DIST) weatherforecast.pro ..\..\qt\6.9.0\msvc2022_64\mkspecs\features\spec_pre.prf ..\..\qt\6.9.0\msvc2022_64\mkspecs\common\windows-desktop.conf ..\..\qt\6.9.0\msvc2022_64\mkspecs\features\win32\windows_vulkan_sdk.prf ..\..\qt\6.9.0\msvc2022_64\mkspecs\common\windows-vulkan.conf ..\..\qt\6.9.0\msvc2022_64\mkspecs\common\msvc-desktop.conf ..\..\qt\6.9.0\msvc2022_64\mkspecs\qconfig.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_ext_freetype.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_ext_libjpeg.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_ext_libpng.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_ext_openxr_loader.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_3danimation.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_3danimation_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_3dcore.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_3dcore_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_3dextras.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_3dextras_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_3dinput.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_3dinput_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_3dlogic.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_3dlogic_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_3dquick.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_3dquick_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_3dquickanimation.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_3dquickanimation_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_3dquickextras.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_3dquickextras_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_3dquickinput.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_3dquickinput_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_3dquickrender.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_3dquickrender_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_3dquickscene2d.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_3dquickscene2d_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_3dquickscene3d.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_3dquickscene3d_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_3drender.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_3drender_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_activeqt.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_activeqt_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_axbase_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_axcontainer.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_axcontainer_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_axserver.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_axserver_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_bluetooth.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_bluetooth_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_bodymovin_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_charts.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_charts_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_chartsqml.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_chartsqml_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_concurrent.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_concurrent_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_connectivity_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_core.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_core5compat.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_core5compat_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_core_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_datavisualization.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_datavisualization_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_datavisualizationqml.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_datavisualizationqml_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_dbus.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_dbus_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_designer.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_designer_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_designercomponents_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_devicediscovery_support_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_entrypoint_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_example_icons_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_examples_asset_downloader_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_fb_support_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_ffmpegmediapluginimpl_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_freetype_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_graphs.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_graphs_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_graphswidgets.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_graphswidgets_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_grpc.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_grpc_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_grpcquick.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_grpcquick_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_gui.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_gui_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_harfbuzz_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_help.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_help_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_httpserver.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_httpserver_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_jpeg_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_jsonrpc_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_labsanimation.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_labsanimation_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_labsfolderlistmodel.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_labsfolderlistmodel_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_labsplatform.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_labsplatform_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_labsqmlmodels.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_labsqmlmodels_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_labssettings.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_labssettings_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_labssharedimage.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_labssharedimage_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_labswavefrontmesh.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_labswavefrontmesh_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_languageserver_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_linguist.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_location.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_location_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_multimedia.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_multimedia_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_multimediaquick_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_multimediatestlibprivate_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_multimediawidgets.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_multimediawidgets_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_network.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_network_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_networkauth.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_networkauth_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_nfc.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_nfc_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_opengl.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_opengl_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_openglwidgets.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_openglwidgets_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_packetprotocol_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_png_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_positioning.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_positioning_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_positioningquick.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_positioningquick_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_printsupport.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_printsupport_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_protobuf.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_protobuf_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_protobufqtcoretypes.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_protobufqtcoretypes_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_protobufqtguitypes.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_protobufqtguitypes_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_protobufquick.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_protobufquick_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_protobufwellknowntypes.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_protobufwellknowntypes_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_qdoccatch_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_qdoccatchconversions_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_qdoccatchgenerators_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_qml.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_qml_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_qmlassetdownloader.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_qmlassetdownloader_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_qmlcompiler.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_qmlcompiler_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_qmlcore.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_qmlcore_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_qmldebug_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_qmldom_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_qmlformat_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_qmlintegration.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_qmlintegration_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_qmllocalstorage.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_qmllocalstorage_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_qmlls_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_qmlmeta.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_qmlmeta_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_qmlmodels.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_qmlmodels_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_qmlnetwork.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_qmlnetwork_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_qmltest.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_qmltest_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_qmltoolingsettings_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_qmltyperegistrar_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_qmlworkerscript.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_qmlworkerscript_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_qmlxmllistmodel.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_qmlxmllistmodel_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quick.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quick3d.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quick3d_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quick3dassetimport.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quick3dassetimport_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quick3dassetutils.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quick3dassetutils_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quick3deffects.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quick3deffects_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quick3dglslparser_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quick3dhelpers.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quick3dhelpers_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quick3dhelpersimpl.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quick3dhelpersimpl_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quick3diblbaker.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quick3diblbaker_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quick3dparticleeffects.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quick3dparticleeffects_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quick3dparticles.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quick3dparticles_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quick3dphysics.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quick3dphysics_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quick3dphysicshelpers.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quick3dphysicshelpers_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quick3druntimerender.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quick3druntimerender_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quick3dspatialaudio_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quick3dutils.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quick3dutils_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quick3dxr.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quick3dxr_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quick_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2basic.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2basic_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2basicstyleimpl.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2basicstyleimpl_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2fluentwinui3styleimpl.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2fluentwinui3styleimpl_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2fusion.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2fusion_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2fusionstyleimpl.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2fusionstyleimpl_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2imagine.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2imagine_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2imaginestyleimpl.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2imaginestyleimpl_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2impl.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2impl_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2material.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2material_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2materialstyleimpl.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2materialstyleimpl_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2universal.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2universal_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2universalstyleimpl.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2universalstyleimpl_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2windowsstyleimpl.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2windowsstyleimpl_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quickcontrolstestutilsprivate_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quickdialogs2.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quickdialogs2_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quickdialogs2quickimpl.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quickdialogs2quickimpl_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quickdialogs2utils.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quickdialogs2utils_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quickeffects.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quickeffects_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quicklayouts.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quicklayouts_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quickparticles_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quickshapes_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quicktemplates2.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quicktemplates2_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quicktestutilsprivate_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quicktimeline.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quicktimeline_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quicktimelineblendtrees.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quicktimelineblendtrees_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quickvectorimage.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quickvectorimage_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quickvectorimagegenerator_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quickwidgets.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_quickwidgets_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_remoteobjects.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_remoteobjects_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_remoteobjectsqml.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_remoteobjectsqml_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_repparser.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_repparser_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_scxml.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_scxml_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_scxmlglobal_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_scxmlqml.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_scxmlqml_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_sensors.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_sensors_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_sensorsquick.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_sensorsquick_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_serialbus.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_serialbus_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_serialport.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_serialport_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_shadertools.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_shadertools_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_spatialaudio.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_spatialaudio_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_sql.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_sql_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_statemachine.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_statemachine_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_statemachineqml.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_statemachineqml_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_svg.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_svg_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_svgwidgets.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_svgwidgets_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_testinternals_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_testlib.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_testlib_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_texttospeech.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_texttospeech_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_tools_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_uiplugin.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_uitools.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_uitools_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_virtualkeyboard.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_virtualkeyboard_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_virtualkeyboardqml.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_virtualkeyboardqml_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_virtualkeyboardsettings.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_virtualkeyboardsettings_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_webchannel.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_webchannel_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_webchannelquick.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_webchannelquick_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_websockets.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_websockets_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_webview.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_webview_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_webviewquick.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_webviewquick_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_widgets.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_widgets_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_xml.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_xml_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\modules\qt_lib_zlib_private.pri ..\..\qt\6.9.0\msvc2022_64\mkspecs\features\qt_functions.prf ..\..\qt\6.9.0\msvc2022_64\mkspecs\features\qt_config.prf ..\..\qt\6.9.0\msvc2022_64\mkspecs\win32-msvc\qmake.conf ..\..\qt\6.9.0\msvc2022_64\mkspecs\features\spec_post.prf .qmake.stash ..\..\qt\6.9.0\msvc2022_64\mkspecs\features\exclusive_builds.prf ..\..\qt\6.9.0\msvc2022_64\mkspecs\common\msvc-version.conf ..\..\qt\6.9.0\msvc2022_64\mkspecs\features\toolchain.prf ..\..\qt\6.9.0\msvc2022_64\mkspecs\features\default_pre.prf ..\..\qt\6.9.0\msvc2022_64\mkspecs\features\win32\default_pre.prf ..\..\qt\6.9.0\msvc2022_64\mkspecs\features\resolve_config.prf ..\..\qt\6.9.0\msvc2022_64\mkspecs\features\exclusive_builds_post.prf ..\..\qt\6.9.0\msvc2022_64\mkspecs\features\default_post.prf ..\..\qt\6.9.0\msvc2022_64\mkspecs\features\build_pass.prf ..\..\qt\6.9.0\msvc2022_64\mkspecs\features\qml_debug.prf ..\..\qt\6.9.0\msvc2022_64\mkspecs\features\precompile_header.prf ..\..\qt\6.9.0\msvc2022_64\mkspecs\features\warn_on.prf ..\..\qt\6.9.0\msvc2022_64\mkspecs\features\permissions.prf ..\..\qt\6.9.0\msvc2022_64\mkspecs\features\qt.prf ..\..\qt\6.9.0\msvc2022_64\mkspecs\features\resources_functions.prf ..\..\qt\6.9.0\msvc2022_64\mkspecs\features\resources.prf ..\..\qt\6.9.0\msvc2022_64\mkspecs\features\moc.prf ..\..\qt\6.9.0\msvc2022_64\mkspecs\features\win32\opengl.prf ..\..\qt\6.9.0\msvc2022_64\mkspecs\features\uic.prf ..\..\qt\6.9.0\msvc2022_64\mkspecs\features\qmake_use.prf ..\..\qt\6.9.0\msvc2022_64\mkspecs\features\file_copies.prf ..\..\qt\6.9.0\msvc2022_64\mkspecs\features\win32\windows.prf ..\..\qt\6.9.0\msvc2022_64\mkspecs\features\testcase_targets.prf ..\..\qt\6.9.0\msvc2022_64\mkspecs\features\exceptions.prf ..\..\qt\6.9.0\msvc2022_64\mkspecs\features\yacc.prf ..\..\qt\6.9.0\msvc2022_64\mkspecs\features\lex.prf weatherforecast.pro res.qrc ..\..\qt\6.9.0\msvc2022_64\lib\Qt6Widgets.prl ..\..\qt\6.9.0\msvc2022_64\lib\Qt6Gui.prl ..\..\qt\6.9.0\msvc2022_64\lib\Qt6Network.prl ..\..\qt\6.9.0\msvc2022_64\lib\Qt6Core.prl ..\..\qt\6.9.0\msvc2022_64\lib\Qt6EntryPoint.prl   res.qrc ..\..\qt\6.9.0\msvc2022_64\mkspecs\features\data\dummy.cpp citycodeutils.h widget.h  citycodeutils.cpp main.cpp widget.cpp widget.ui    

clean: compiler_clean 
	-$(DEL_FILE) release\citycodeutils.obj release\main.obj release\widget.obj release\qrc_res.obj release\moc_widget.obj

distclean: clean 
	-$(DEL_FILE) .qmake.stash
	-$(DEL_FILE) $(DESTDIR_TARGET)
	-$(DEL_FILE) Makefile.Release

mocclean: compiler_moc_header_clean compiler_moc_objc_header_clean compiler_moc_source_clean

mocables: compiler_moc_header_make_all compiler_moc_objc_header_make_all compiler_moc_source_make_all

check: first

benchmark: first

compiler_no_pch_compiler_make_all:
compiler_no_pch_compiler_clean:
compiler_rcc_make_all: release\qrc_res.cpp
compiler_rcc_clean:
	-$(DEL_FILE) release\qrc_res.cpp
release\qrc_res.cpp: res.qrc \
		..\..\qt\6.9.0\msvc2022_64\bin\rcc.exe \
		res\2.png \
		res\leizhenyubanyoubingbao.png \
		res\zhongxue.png \
		res\3.png \
		res\4.png \
		res\yin.png \
		res\baoyu.png \
		res\dayu.png \
		res\DaBaoYuDaoTeDaBaoYu.png \
		res\1.png \
		res\search.png \
		res\xiaodaozhongyu.png \
		res\dongyu.png \
		res\sun.png \
		res\leizhenyu.png \
		res\zhongdaodayu.png \
		res\yujiaxue.png \
		res\qiangshachenbao.png \
		res\zhenxue.png \
		res\dadaobaoxue.png \
		res\zhongdaodaxue.png \
		res\daxue.png \
		res\duoyun.png \
		res\tedabaoyu.png \
		res\xue.png \
		res\yangchen.png \
		res\zhongyu.png \
		res\wu.png \
		res\xiaoxue.png \
		res\data.json \
		res\fuchen.png \
		res\baoxue.png \
		res\zhenyu.png \
		res\dabaoyu.png \
		res\xiaoyu.png \
		res\zhonxue.png \
		res\xiaodaozhonxue.png \
		res\mai.png \
		res\close.png \
		res\leibaoyu.png \
		res\shachenbao.png
	E:\qt\6.9.0\msvc2022_64\bin\rcc.exe -name res --no-zstd res.qrc -o release\qrc_res.cpp

compiler_moc_predefs_make_all: release\moc_predefs.h
compiler_moc_predefs_clean:
	-$(DEL_FILE) release\moc_predefs.h
release\moc_predefs.h: ..\..\qt\6.9.0\msvc2022_64\mkspecs\features\data\dummy.cpp
	cl -BxE:\qt\6.9.0\msvc2022_64\bin\qmake.exe -nologo -Zc:wchar_t -FS -Zc:rvalueCast -Zc:inline -Zc:strictStrings -Zc:throwingNew -permissive- -Zc:__cplusplus -Zc:externConstexpr -O2 -MD -std:c++17 -utf-8 -W3 -w34100 -w34189 -w44456 -w44457 -w44458 -wd4577 -wd4467 -E ..\..\qt\6.9.0\msvc2022_64\mkspecs\features\data\dummy.cpp 2>NUL >release\moc_predefs.h

compiler_moc_header_make_all: release\moc_widget.cpp
compiler_moc_header_clean:
	-$(DEL_FILE) release\moc_widget.cpp
release\moc_widget.cpp: widget.h \
		citycodeutils.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\QString \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qstring.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qchar.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qglobal.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtcoreglobal.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtversionchecks.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtconfiginclude.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qconfig.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtcore-config.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtconfigmacros.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtdeprecationdefinitions.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qcompilerdetection.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qprocessordetection.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qsystemdetection.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtcoreexports.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtdeprecationmarkers.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtclasshelpermacros.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtpreprocessorsupport.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qassert.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtnoop.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtypes.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtversion.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtypeinfo.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qcontainerfwd.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qsysinfo.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qlogging.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qflags.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qcompare_impl.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qatomic.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qbasicatomic.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qatomic_cxx11.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qgenericatomic.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qyieldcpu.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qconstructormacros.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qdarwinhelpers.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qexceptionhandling.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qforeach.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qttypetraits.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qfunctionpointer.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qglobalstatic.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qmalloc.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qminmax.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qnumeric.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qoverload.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qswap.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtenvironmentvariables.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtresource.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qttranslation.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qversiontagging.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qcompare.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qstdlibdetection.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qcomparehelpers.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\q20type_traits.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qstringview.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qbytearray.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qrefcount.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qnamespace.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtmetamacros.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qarraydata.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qpair.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qarraydatapointer.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qarraydataops.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qcontainertools_impl.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qxptype_traits.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\q20functional.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\q20memory.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\q17memory.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qbytearrayalgorithms.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qbytearrayview.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qstringfwd.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qstringliteral.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qstringalgorithms.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qlatin1stringview.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qanystringview.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qutf8stringview.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qstringtokenizer.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qstringbuilder.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qstringconverter.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qstringconverter_base.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\QMap \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qmap.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qhashfunctions.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qiterator.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qlist.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qbytearraylist.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qstringlist.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qalgorithms.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qstringmatcher.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qshareddata.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qshareddata_impl.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtWidgets\QWidget \
		..\..\qt\6.9.0\msvc2022_64\include\QtWidgets\qwidget.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtWidgets\qtwidgetsglobal.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qtguiglobal.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qtgui-config.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qtguiexports.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtWidgets\qtwidgets-config.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtWidgets\qtwidgetsexports.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qwindowdefs.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qobjectdefs.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qobjectdefs_impl.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qfunctionaltools_impl.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qwindowdefs_win.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qobject.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qcoreevent.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qbasictimer.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qabstracteventdispatcher.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qeventloop.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qdeadlinetimer.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qelapsedtimer.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qmetatype.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qdatastream.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qscopedpointer.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qiodevicebase.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qfloat16.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qmath.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtformat_impl.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qiterable.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qmetacontainer.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qcontainerinfo.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtaggedpointer.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qscopeguard.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qobject_impl.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qbindingstorage.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qmargins.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\q23utility.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\q20utility.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qaction.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qkeysequence.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qicon.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qsize.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qpixmap.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qpaintdevice.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qrect.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qpoint.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qcolor.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qrgb.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qrgba64.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qimage.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qpixelformat.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qtransform.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qpolygon.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qregion.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qspan.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\q20iterator.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qline.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qvariant.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qdebug.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtextstream.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qcontiguouscache.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qsharedpointer.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qsharedpointer_impl.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qset.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qhash.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qvarlengtharray.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qpalette.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qbrush.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qfont.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qendian.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qfontmetrics.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qfontinfo.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qfontvariableaxis.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtWidgets\qsizepolicy.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qcursor.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qbitmap.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qevent.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qiodevice.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qurl.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qeventpoint.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qvector2d.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qvectornd.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qpointingdevice.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qinputdevice.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qscreen.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\QList \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\QObject \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\QRect \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\QSize \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\QSizeF \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\QTransform \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qnativeinterface.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qscreen_platform.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qguiapplication.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qcoreapplication.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qcoreapplication_platform.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qfuture.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qfutureinterface.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qmutex.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtsan_impl.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qresultstore.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qfuture_impl.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qthreadpool.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qthread.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qrunnable.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qexception.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qpromise.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qinputmethod.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qlocale.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qguiapplication_platform.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\QMouseEvent \
		..\..\qt\6.9.0\msvc2022_64\include\QtWidgets\QMenu \
		..\..\qt\6.9.0\msvc2022_64\include\QtWidgets\qmenu.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtNetwork\QNetworkAccessManager \
		..\..\qt\6.9.0\msvc2022_64\include\QtNetwork\qnetworkaccessmanager.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtNetwork\qtnetworkglobal.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtNetwork\qtnetwork-config.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtNetwork\qtnetworkexports.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtNetwork\qnetworkrequest.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtNetwork\qhttpheaders.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\QSharedDataPointer \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\QUrl \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\QVariant \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\q26numeric.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtNetwork\QSslConfiguration \
		..\..\qt\6.9.0\msvc2022_64\include\QtNetwork\qsslconfiguration.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtNetwork\qsslsocket.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtNetwork\qtcpsocket.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtNetwork\qabstractsocket.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtNetwork\qhostaddress.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtNetwork\qsslerror.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtNetwork\qsslcertificate.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qcryptographichash.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qdatetime.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qcalendar.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtNetwork\qssl.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\QFlags \
		..\..\qt\6.9.0\msvc2022_64\include\QtNetwork\QSslPreSharedKeyAuthenticator \
		..\..\qt\6.9.0\msvc2022_64\include\QtNetwork\qsslpresharedkeyauthenticator.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\QMetaType \
		..\..\qt\6.9.0\msvc2022_64\include\QtNetwork\QNetworkRequest \
		..\..\qt\6.9.0\msvc2022_64\include\QtNetwork\QNetworkReply \
		..\..\qt\6.9.0\msvc2022_64\include\QtNetwork\qnetworkreply.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\QIODevice \
		..\..\qt\6.9.0\msvc2022_64\include\QtWidgets\QMessageBox \
		..\..\qt\6.9.0\msvc2022_64\include\QtWidgets\qmessagebox.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtWidgets\qdialog.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtWidgets\qdialogbuttonbox.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\QJsonDocument \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qjsondocument.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qjsonparseerror.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qjsonvalue.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qcborvalue.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qcborcommon.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qregularexpression.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\quuid.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\QJsonObject \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qjsonobject.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\QJsonArray \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qjsonarray.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\QFile \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qfile.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qfiledevice.h \
		release\moc_predefs.h \
		..\..\qt\6.9.0\msvc2022_64\bin\moc.exe
	E:\qt\6.9.0\msvc2022_64\bin\moc.exe $(DEFINES) --compiler-flavor=msvc --include E:/qtprogram/weatherforecast/release/moc_predefs.h -IE:/qt/6.9.0/msvc2022_64/mkspecs/win32-msvc -IE:/qtprogram/weatherforecast -IE:/qt/6.9.0/msvc2022_64/include -IE:/qt/6.9.0/msvc2022_64/include/QtWidgets -IE:/qt/6.9.0/msvc2022_64/include/QtGui -IE:/qt/6.9.0/msvc2022_64/include/QtNetwork -IE:/qt/6.9.0/msvc2022_64/include/QtCore -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.39.33519\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.39.33519\ATLMFC\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" widget.h -o release\moc_widget.cpp

compiler_moc_objc_header_make_all:
compiler_moc_objc_header_clean:
compiler_moc_source_make_all:
compiler_moc_source_clean:
compiler_uic_make_all: ui_widget.h
compiler_uic_clean:
	-$(DEL_FILE) ui_widget.h
ui_widget.h: widget.ui \
		..\..\qt\6.9.0\msvc2022_64\bin\uic.exe
	E:\qt\6.9.0\msvc2022_64\bin\uic.exe widget.ui -o ui_widget.h

compiler_yacc_decl_make_all:
compiler_yacc_decl_clean:
compiler_yacc_impl_make_all:
compiler_yacc_impl_clean:
compiler_lex_make_all:
compiler_lex_clean:
compiler_clean: compiler_rcc_clean compiler_moc_predefs_clean compiler_moc_header_clean compiler_uic_clean 



####### Compile

release\citycodeutils.obj: citycodeutils.cpp citycodeutils.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\QString \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qstring.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qchar.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qglobal.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtcoreglobal.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtversionchecks.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtconfiginclude.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qconfig.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtcore-config.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtconfigmacros.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtdeprecationdefinitions.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qcompilerdetection.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qprocessordetection.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qsystemdetection.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtcoreexports.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtdeprecationmarkers.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtclasshelpermacros.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtpreprocessorsupport.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qassert.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtnoop.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtypes.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtversion.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtypeinfo.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qcontainerfwd.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qsysinfo.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qlogging.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qflags.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qcompare_impl.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qatomic.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qbasicatomic.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qatomic_cxx11.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qgenericatomic.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qyieldcpu.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qconstructormacros.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qdarwinhelpers.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qexceptionhandling.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qforeach.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qttypetraits.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qfunctionpointer.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qglobalstatic.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qmalloc.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qminmax.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qnumeric.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qoverload.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qswap.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtenvironmentvariables.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtresource.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qttranslation.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qversiontagging.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qcompare.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qstdlibdetection.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qcomparehelpers.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\q20type_traits.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qstringview.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qbytearray.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qrefcount.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qnamespace.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtmetamacros.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qarraydata.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qpair.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qarraydatapointer.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qarraydataops.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qcontainertools_impl.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qxptype_traits.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\q20functional.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\q20memory.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\q17memory.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qbytearrayalgorithms.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qbytearrayview.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qstringfwd.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qstringliteral.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qstringalgorithms.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qlatin1stringview.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qanystringview.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qutf8stringview.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qstringtokenizer.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qstringbuilder.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qstringconverter.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qstringconverter_base.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\QMap \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qmap.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qhashfunctions.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qiterator.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qlist.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qbytearraylist.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qstringlist.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qalgorithms.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qstringmatcher.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qshareddata.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qshareddata_impl.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\QJsonDocument \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qjsondocument.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qjsonparseerror.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qjsonvalue.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qcborvalue.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qcborcommon.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qobjectdefs.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qobjectdefs_impl.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qfunctionaltools_impl.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qmetatype.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qdatastream.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qscopedpointer.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qiodevicebase.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qfloat16.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qmath.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtformat_impl.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qiterable.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qmetacontainer.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qcontainerinfo.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtaggedpointer.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qscopeguard.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qdatetime.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qcalendar.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qlocale.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qvariant.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qdebug.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtextstream.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qcontiguouscache.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qsharedpointer.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qsharedpointer_impl.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qset.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qhash.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qvarlengtharray.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\q23utility.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\q20utility.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qobject.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qcoreevent.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qbasictimer.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qabstracteventdispatcher.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qeventloop.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qdeadlinetimer.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qelapsedtimer.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qobject_impl.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qbindingstorage.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qregularexpression.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qurl.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\quuid.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qendian.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\QFile \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qfile.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qfiledevice.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qiodevice.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qspan.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\q20iterator.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\QJsonArray \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qjsonarray.h

release\main.obj: main.cpp widget.h \
		citycodeutils.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\QString \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qstring.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qchar.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qglobal.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtcoreglobal.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtversionchecks.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtconfiginclude.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qconfig.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtcore-config.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtconfigmacros.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtdeprecationdefinitions.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qcompilerdetection.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qprocessordetection.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qsystemdetection.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtcoreexports.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtdeprecationmarkers.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtclasshelpermacros.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtpreprocessorsupport.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qassert.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtnoop.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtypes.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtversion.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtypeinfo.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qcontainerfwd.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qsysinfo.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qlogging.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qflags.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qcompare_impl.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qatomic.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qbasicatomic.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qatomic_cxx11.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qgenericatomic.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qyieldcpu.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qconstructormacros.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qdarwinhelpers.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qexceptionhandling.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qforeach.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qttypetraits.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qfunctionpointer.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qglobalstatic.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qmalloc.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qminmax.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qnumeric.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qoverload.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qswap.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtenvironmentvariables.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtresource.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qttranslation.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qversiontagging.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qcompare.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qstdlibdetection.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qcomparehelpers.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\q20type_traits.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qstringview.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qbytearray.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qrefcount.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qnamespace.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtmetamacros.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qarraydata.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qpair.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qarraydatapointer.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qarraydataops.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qcontainertools_impl.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qxptype_traits.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\q20functional.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\q20memory.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\q17memory.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qbytearrayalgorithms.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qbytearrayview.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qstringfwd.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qstringliteral.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qstringalgorithms.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qlatin1stringview.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qanystringview.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qutf8stringview.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qstringtokenizer.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qstringbuilder.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qstringconverter.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qstringconverter_base.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\QMap \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qmap.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qhashfunctions.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qiterator.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qlist.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qbytearraylist.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qstringlist.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qalgorithms.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qstringmatcher.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qshareddata.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qshareddata_impl.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtWidgets\QWidget \
		..\..\qt\6.9.0\msvc2022_64\include\QtWidgets\qwidget.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtWidgets\qtwidgetsglobal.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qtguiglobal.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qtgui-config.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qtguiexports.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtWidgets\qtwidgets-config.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtWidgets\qtwidgetsexports.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qwindowdefs.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qobjectdefs.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qobjectdefs_impl.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qfunctionaltools_impl.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qwindowdefs_win.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qobject.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qcoreevent.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qbasictimer.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qabstracteventdispatcher.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qeventloop.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qdeadlinetimer.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qelapsedtimer.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qmetatype.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qdatastream.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qscopedpointer.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qiodevicebase.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qfloat16.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qmath.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtformat_impl.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qiterable.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qmetacontainer.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qcontainerinfo.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtaggedpointer.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qscopeguard.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qobject_impl.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qbindingstorage.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qmargins.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\q23utility.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\q20utility.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qaction.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qkeysequence.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qicon.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qsize.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qpixmap.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qpaintdevice.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qrect.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qpoint.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qcolor.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qrgb.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qrgba64.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qimage.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qpixelformat.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qtransform.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qpolygon.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qregion.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qspan.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\q20iterator.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qline.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qvariant.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qdebug.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtextstream.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qcontiguouscache.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qsharedpointer.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qsharedpointer_impl.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qset.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qhash.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qvarlengtharray.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qpalette.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qbrush.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qfont.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qendian.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qfontmetrics.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qfontinfo.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qfontvariableaxis.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtWidgets\qsizepolicy.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qcursor.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qbitmap.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qevent.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qiodevice.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qurl.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qeventpoint.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qvector2d.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qvectornd.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qpointingdevice.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qinputdevice.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qscreen.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\QList \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\QObject \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\QRect \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\QSize \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\QSizeF \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\QTransform \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qnativeinterface.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qscreen_platform.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qguiapplication.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qcoreapplication.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qcoreapplication_platform.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qfuture.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qfutureinterface.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qmutex.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtsan_impl.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qresultstore.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qfuture_impl.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qthreadpool.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qthread.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qrunnable.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qexception.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qpromise.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qinputmethod.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qlocale.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qguiapplication_platform.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\QMouseEvent \
		..\..\qt\6.9.0\msvc2022_64\include\QtWidgets\QMenu \
		..\..\qt\6.9.0\msvc2022_64\include\QtWidgets\qmenu.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtNetwork\QNetworkAccessManager \
		..\..\qt\6.9.0\msvc2022_64\include\QtNetwork\qnetworkaccessmanager.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtNetwork\qtnetworkglobal.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtNetwork\qtnetwork-config.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtNetwork\qtnetworkexports.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtNetwork\qnetworkrequest.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtNetwork\qhttpheaders.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\QSharedDataPointer \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\QUrl \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\QVariant \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\q26numeric.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtNetwork\QSslConfiguration \
		..\..\qt\6.9.0\msvc2022_64\include\QtNetwork\qsslconfiguration.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtNetwork\qsslsocket.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtNetwork\qtcpsocket.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtNetwork\qabstractsocket.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtNetwork\qhostaddress.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtNetwork\qsslerror.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtNetwork\qsslcertificate.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qcryptographichash.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qdatetime.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qcalendar.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtNetwork\qssl.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\QFlags \
		..\..\qt\6.9.0\msvc2022_64\include\QtNetwork\QSslPreSharedKeyAuthenticator \
		..\..\qt\6.9.0\msvc2022_64\include\QtNetwork\qsslpresharedkeyauthenticator.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\QMetaType \
		..\..\qt\6.9.0\msvc2022_64\include\QtNetwork\QNetworkRequest \
		..\..\qt\6.9.0\msvc2022_64\include\QtNetwork\QNetworkReply \
		..\..\qt\6.9.0\msvc2022_64\include\QtNetwork\qnetworkreply.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\QIODevice \
		..\..\qt\6.9.0\msvc2022_64\include\QtWidgets\QMessageBox \
		..\..\qt\6.9.0\msvc2022_64\include\QtWidgets\qmessagebox.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtWidgets\qdialog.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtWidgets\qdialogbuttonbox.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\QJsonDocument \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qjsondocument.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qjsonparseerror.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qjsonvalue.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qcborvalue.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qcborcommon.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qregularexpression.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\quuid.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\QJsonObject \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qjsonobject.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\QJsonArray \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qjsonarray.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\QFile \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qfile.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qfiledevice.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtWidgets\QApplication \
		..\..\qt\6.9.0\msvc2022_64\include\QtWidgets\qapplication.h

release\widget.obj: widget.cpp widget.h \
		citycodeutils.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\QString \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qstring.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qchar.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qglobal.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtcoreglobal.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtversionchecks.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtconfiginclude.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qconfig.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtcore-config.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtconfigmacros.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtdeprecationdefinitions.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qcompilerdetection.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qprocessordetection.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qsystemdetection.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtcoreexports.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtdeprecationmarkers.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtclasshelpermacros.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtpreprocessorsupport.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qassert.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtnoop.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtypes.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtversion.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtypeinfo.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qcontainerfwd.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qsysinfo.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qlogging.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qflags.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qcompare_impl.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qatomic.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qbasicatomic.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qatomic_cxx11.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qgenericatomic.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qyieldcpu.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qconstructormacros.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qdarwinhelpers.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qexceptionhandling.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qforeach.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qttypetraits.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qfunctionpointer.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qglobalstatic.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qmalloc.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qminmax.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qnumeric.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qoverload.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qswap.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtenvironmentvariables.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtresource.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qttranslation.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qversiontagging.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qcompare.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qstdlibdetection.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qcomparehelpers.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\q20type_traits.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qstringview.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qbytearray.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qrefcount.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qnamespace.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtmetamacros.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qarraydata.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qpair.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qarraydatapointer.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qarraydataops.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qcontainertools_impl.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qxptype_traits.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\q20functional.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\q20memory.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\q17memory.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qbytearrayalgorithms.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qbytearrayview.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qstringfwd.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qstringliteral.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qstringalgorithms.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qlatin1stringview.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qanystringview.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qutf8stringview.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qstringtokenizer.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qstringbuilder.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qstringconverter.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qstringconverter_base.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\QMap \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qmap.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qhashfunctions.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qiterator.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qlist.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qbytearraylist.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qstringlist.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qalgorithms.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qstringmatcher.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qshareddata.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qshareddata_impl.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtWidgets\QWidget \
		..\..\qt\6.9.0\msvc2022_64\include\QtWidgets\qwidget.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtWidgets\qtwidgetsglobal.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qtguiglobal.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qtgui-config.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qtguiexports.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtWidgets\qtwidgets-config.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtWidgets\qtwidgetsexports.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qwindowdefs.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qobjectdefs.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qobjectdefs_impl.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qfunctionaltools_impl.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qwindowdefs_win.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qobject.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qcoreevent.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qbasictimer.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qabstracteventdispatcher.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qeventloop.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qdeadlinetimer.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qelapsedtimer.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qmetatype.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qdatastream.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qscopedpointer.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qiodevicebase.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qfloat16.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qmath.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtformat_impl.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qiterable.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qmetacontainer.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qcontainerinfo.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtaggedpointer.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qscopeguard.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qobject_impl.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qbindingstorage.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qmargins.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\q23utility.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\q20utility.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qaction.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qkeysequence.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qicon.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qsize.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qpixmap.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qpaintdevice.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qrect.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qpoint.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qcolor.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qrgb.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qrgba64.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qimage.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qpixelformat.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qtransform.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qpolygon.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qregion.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qspan.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\q20iterator.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qline.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qvariant.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qdebug.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtextstream.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qcontiguouscache.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qsharedpointer.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qsharedpointer_impl.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qset.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qhash.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qvarlengtharray.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qpalette.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qbrush.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qfont.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qendian.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qfontmetrics.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qfontinfo.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qfontvariableaxis.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtWidgets\qsizepolicy.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qcursor.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qbitmap.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qevent.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qiodevice.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qurl.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qeventpoint.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qvector2d.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qvectornd.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qpointingdevice.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qinputdevice.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qscreen.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\QList \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\QObject \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\QRect \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\QSize \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\QSizeF \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\QTransform \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qnativeinterface.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qscreen_platform.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qguiapplication.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qcoreapplication.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qcoreapplication_platform.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qfuture.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qfutureinterface.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qmutex.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qtsan_impl.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qresultstore.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qfuture_impl.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qthreadpool.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qthread.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qrunnable.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qexception.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qpromise.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qinputmethod.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qlocale.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qguiapplication_platform.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\QMouseEvent \
		..\..\qt\6.9.0\msvc2022_64\include\QtWidgets\QMenu \
		..\..\qt\6.9.0\msvc2022_64\include\QtWidgets\qmenu.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtNetwork\QNetworkAccessManager \
		..\..\qt\6.9.0\msvc2022_64\include\QtNetwork\qnetworkaccessmanager.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtNetwork\qtnetworkglobal.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtNetwork\qtnetwork-config.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtNetwork\qtnetworkexports.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtNetwork\qnetworkrequest.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtNetwork\qhttpheaders.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\QSharedDataPointer \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\QUrl \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\QVariant \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\q26numeric.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtNetwork\QSslConfiguration \
		..\..\qt\6.9.0\msvc2022_64\include\QtNetwork\qsslconfiguration.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtNetwork\qsslsocket.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtNetwork\qtcpsocket.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtNetwork\qabstractsocket.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtNetwork\qhostaddress.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtNetwork\qsslerror.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtNetwork\qsslcertificate.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qcryptographichash.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qdatetime.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qcalendar.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtNetwork\qssl.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\QFlags \
		..\..\qt\6.9.0\msvc2022_64\include\QtNetwork\QSslPreSharedKeyAuthenticator \
		..\..\qt\6.9.0\msvc2022_64\include\QtNetwork\qsslpresharedkeyauthenticator.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\QMetaType \
		..\..\qt\6.9.0\msvc2022_64\include\QtNetwork\QNetworkRequest \
		..\..\qt\6.9.0\msvc2022_64\include\QtNetwork\QNetworkReply \
		..\..\qt\6.9.0\msvc2022_64\include\QtNetwork\qnetworkreply.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\QIODevice \
		..\..\qt\6.9.0\msvc2022_64\include\QtWidgets\QMessageBox \
		..\..\qt\6.9.0\msvc2022_64\include\QtWidgets\qmessagebox.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtWidgets\qdialog.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtWidgets\qdialogbuttonbox.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\QJsonDocument \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qjsondocument.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qjsonparseerror.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qjsonvalue.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qcborvalue.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qcborcommon.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qregularexpression.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\quuid.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\QJsonObject \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qjsonobject.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\QJsonArray \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qjsonarray.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\QFile \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qfile.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtCore\qfiledevice.h \
		ui_widget.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\QIcon \
		..\..\qt\6.9.0\msvc2022_64\include\QtWidgets\QApplication \
		..\..\qt\6.9.0\msvc2022_64\include\QtWidgets\qapplication.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtWidgets\QGridLayout \
		..\..\qt\6.9.0\msvc2022_64\include\QtWidgets\qgridlayout.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtWidgets\qlayout.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtWidgets\qlayoutitem.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtWidgets\qboxlayout.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtWidgets\QHBoxLayout \
		..\..\qt\6.9.0\msvc2022_64\include\QtWidgets\QLabel \
		..\..\qt\6.9.0\msvc2022_64\include\QtWidgets\qlabel.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtWidgets\qframe.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qpicture.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qtextdocument.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtWidgets\QLineEdit \
		..\..\qt\6.9.0\msvc2022_64\include\QtWidgets\qlineedit.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qtextcursor.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qtextformat.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qpen.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtGui\qtextoption.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtWidgets\QPushButton \
		..\..\qt\6.9.0\msvc2022_64\include\QtWidgets\qpushbutton.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtWidgets\qabstractbutton.h \
		..\..\qt\6.9.0\msvc2022_64\include\QtWidgets\QSpacerItem \
		..\..\qt\6.9.0\msvc2022_64\include\QtWidgets\QVBoxLayout

release\qrc_res.obj: release\qrc_res.cpp 

release\moc_widget.obj: release\moc_widget.cpp 

####### Install

install:  FORCE

uninstall:  FORCE

FORCE:

